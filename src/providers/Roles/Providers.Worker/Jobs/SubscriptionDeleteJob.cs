﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Jobs
{
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services.AccessConnector;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Services.DatabricksAccountsManager;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Configuration;
    using Microsoft.WindowsAzure.Storage;
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Threading.Tasks;

    /// <summary>
    /// The Subscription Deletion job.
    /// </summary>
    [JobCallback(Name = "SubscriptionDeleteJob")]
    public class SubscriptionDeleteJob : ApplianceJob<SubscriptionDeleteJobMetadata>
    {
        private readonly IApplianceDataProvider applianceDataProvider;

        private readonly IAccessConnectorDataProvider accessConnectorDataProvider;

        private IDatabricksAccountsManager databricksAccountsManager;

        private IDatabricksCredentialManager databricksCredentialManager;

        private const int BatchSize = 100;

        private const int WaitTimeInMilliSeconds = 100;

        /// <summary>
        /// Gets the databricks credential manager.
        /// </summary>
        protected IDatabricksCredentialManager DatabricksCredentialManager =>
            this.databricksCredentialManager ?? (this.databricksCredentialManager = new DatabricksCredentialManager(this.GetFrontdoorEngine(), this.Logger.EventSource as ICommonEventSource));

        /// <summary>
        /// Gets the databricks accounts manager.
        /// </summary>
        protected IDatabricksAccountsManager DatabricksAccountsManager =>
            this.databricksAccountsManager ?? (this.databricksAccountsManager = new DatabricksAccountsManager(this.GetFrontdoorEngine(), this.Logger.EventSource as ICommonEventSource));

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="SubscriptionDeleteJob" /> class.
        /// </summary>
        /// <param name="jobConfiguration">The job configuration.</param>
        public SubscriptionDeleteJob(ProvidersJobConfiguration jobConfiguration)
            : base(jobConfiguration)
        {
            this.applianceDataProvider = jobConfiguration.ApplicationDataProvidersContainer.ApplianceDataProvider;
            this.accessConnectorDataProvider = jobConfiguration.ApplicationDataProvidersContainer.AccessConnectorDataProvider;
        }

        #endregion

        #region Properties

        /// <summary>
        /// Gets the postpone interval.
        /// </summary>
        protected override TimeSpan PostponeInterval =>
            CloudConfigurationManager.GetConfigurationTimeSpan(
                "Microsoft.WindowsAzure.ResourceStack.Providers.SubscriptionDeleteJobJob.PostponeInterval",
                TimeSpan.FromSeconds(60));

        /// <summary>
        /// Gets the max retries for subscription delete job.
        /// </summary>
        private int MaxRetries =>
            CloudConfigurationManager.GetConfigurationNumber(
                "Microsoft.WindowsAzure.ResourceStack.SubscriptionDeleteJob.MaxRetryAttempts", 10);

        #endregion

        /// <summary>
        /// Handle when job maximum life time has exceeded.
        /// </summary>
        protected override async Task OnJobMaxLifetimeExceeded()
        {
            // Logs the remaining access connectors that are not deleted from databricks side and deletes from RP backend.
            await SegmentedResultHelper.Enumerate(
                (continuationToken, top) => this.GetAccessConnectorDataProvider()
              .FindAccessConnectorsSegmented(this.Metadata.SubscriptionId, top: BatchSize, continuationToken: continuationToken),
                accessConnectorEntities =>
                {
                    if (accessConnectorEntities != null || accessConnectorEntities.Count() > 0)
                    {
                        foreach (var accessConnectorEntity in accessConnectorEntities)
                        {
                            this.Logger.LogError("SubscriptionDeleteJobFailure",
                                $"Delete access connector notification to databricks failed ,ResourceId: {accessConnectorEntity.GetResourceId()}, SubscriptionId: {accessConnectorEntity.SubscriptionId}");
                        }

                        // Delete the access connectors from backend.
                        this.accessConnectorDataProvider.DeleteAccessConnectors(accessConnectorEntities).ConfigureAwait(false);
                    }

                    return Task.FromResult(true);

                }).ConfigureAwait(false);

            // Logs the remaining workspaces that are not deleted from databricks side and deleted from RP backend.
            await SegmentedResultHelper.Enumerate(
                (continuationToken, top) => this.applianceDataProvider
              .FindAppliancesSegmented(this.Metadata.SubscriptionId, top: BatchSize, continuationToken: continuationToken),
                applianceEntities =>
                {
                    if (applianceEntities != null || applianceEntities.Count() > 0)
                    {
                        foreach (var appliance in applianceEntities)
                        {
                            this.Logger.LogError("SubscriptionDeleteJobFailure",
                            $"workspace delete notification to databricks failed ,ResourceId: {appliance.GetFullyQualifiedResourceId()}, SubscriptionId: {appliance.SubscriptionId}");
                        }

                        // Delete the access connectors from backend.
                        this.applianceDataProvider.DeleteAppliances(applianceEntities).ConfigureAwait(false);
                    }

                    return Task.FromResult(true);

                }).ConfigureAwait(false);

            var message = $"The subscription deletion job did not complete within the allowed timeout period '{this.Metadata.ProvidersJobMaxLifetime}'";

            this.Logger.LogError("SubscriptionDeleteJob.OnJobMaxLifetimeExceeded", message);
            this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
            this.Metadata.OperationResponseContent = AsyncOperationResult
                .GetAsyncOperationResult(
                    provisioningState: ProvisioningState.Failed,
                    errorCode: ErrorResponseCode.SubscriptionDeletionTimeout.ToString(),
                    message: message)
                .ToJToken();

            throw new JobExecutionResultException(JobExecutionStatus.Faulted, message, nextMetadata: this.Metadata.ToJson());
        }

        /// <summary>
        /// Executes the job asynchronously.
        /// </summary>
        protected override async Task<JobExecutionResult> OnJobExecute()
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.Logger.LogDebug(operationName, $"Subscription delete job for {this.Metadata.SubscriptionId} started.");

            try
            {
                JobExecutionResult jobExecutionResult;

                var subscriptionNotificationProperties = this.Metadata.NotificationDefinition.Properties.ToObject<SubscriptionNotificationPropertiesDefinition>();

                var registeredFeatures = subscriptionNotificationProperties.RegisteredFeatures == null
                    ? new List<string>() :
                    subscriptionNotificationProperties.RegisteredFeatures.Select(feature => feature.Name).ToList();

                jobExecutionResult = await this.DeleteWorkspacesFromSubscription(subscriptionNotificationProperties, registeredFeatures);

                if (jobExecutionResult != null && jobExecutionResult.Status == JobExecutionStatus.Postponed)
                    return jobExecutionResult;

                this.Logger.LogDebug(operationName, $"workspace deletion is completed for subscription {this.Metadata.SubscriptionId}.");

                jobExecutionResult = await this.DeleteAccessConnectorsFromSubscription(subscriptionId: this.Metadata.SubscriptionId,
                     tenantId: subscriptionNotificationProperties.TenantId,
                     registeredFeatures: registeredFeatures,
                     batchSize: BatchSize);

                if (jobExecutionResult != null &&
                    (jobExecutionResult.Status == JobExecutionStatus.Postponed ||
                    jobExecutionResult.Status == JobExecutionStatus.Failed))
                    return jobExecutionResult;

                this.Logger.LogDebug(operationName, $"Access connector deletion is completed for subscription {this.Metadata.SubscriptionId}.");

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(ProvisioningState.Succeeded).ToJToken();

                this.Logger.LogDebug(operationName, $"Subscription delete job for {this.Metadata.SubscriptionId} succeeded.");

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Succeeded,
                    Message = $"Successfully deleted all resource from subscription {this.Metadata.SubscriptionId}. Completing job",
                    Details = HttpStatusCode.OK.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
            catch (Exception exception)
            {
                this.Logger.LogError(operationName, $"Subscription Deletion {this.Metadata.SubscriptionId} is Faulted with exception '{exception}'. Job Faulted.");

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult.GetAsyncOperationResult(
                        ProvisioningState.Failed,
                        ErrorResponseCode.SubscriptionDeletionError.ToString(),
                        ErrorResponseMessages.SubscriptionDeletionError.ToLocalizedMessage(this.Metadata.SubscriptionId, exception.Message))
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = $"Failed to delete resources from subscription '{this.Metadata.SubscriptionId}', exception '{exception.Message}'. Job Faulted",
                    Details = HttpStatusCode.Conflict.ToString(),
                    NextMetadata = this.Metadata.ToJson()
                };
            }
        }

        /// <summary>
        /// Deletes all the workspaces from subscription.
        /// </summary>
        /// <param name="subscriptionNotificationProperties"> The delete subscription notification properties </param>
        /// <param name="registeredFeatures">List of registered features on subscription</param>
        private async Task<JobExecutionResult> DeleteWorkspacesFromSubscription(
            SubscriptionNotificationPropertiesDefinition subscriptionNotificationProperties,
            List<string> registeredFeatures)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            JobExecutionResult jobExecutionResult = default;

            var applianceEntities = await this.applianceDataProvider.FindAllApplicationForSubscription(
                        subscriptionId: this.Metadata.SubscriptionId)
                    .ConfigureAwait(continueOnCapturedContext: false);

            this.Logger.LogDebug(operationName,
                $"Total appliances {applianceEntities?.Count} will be deleted from the subscription id {this.Metadata.SubscriptionId} ");

            foreach (var appliance in applianceEntities)
            {
                var cloudEnvironment = CloudConfigurationManager.GetConfiguration("CloudEnvironment", "public");

                // Delete workspace notification is only supported for public cloud by Databricks
                // Support to other clouds will be added in coming months and we can remove this check
                if (string.Equals(cloudEnvironment, "public", StringComparison.InvariantCultureIgnoreCase))
                {
                    jobExecutionResult = await this.SendDeleteWorkspaceNotificationToDatabricks(
                            appliance,
                            subscriptionNotificationProperties,
                            registeredFeatures)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    if (jobExecutionResult != null && jobExecutionResult.Status == JobExecutionStatus.Postponed)
                    {
                        return jobExecutionResult;
                    }
                }

                await DeleteAppliances(appliance);
                this.Logger.LogDebug(operationName,
                        $"Workspace deleted '{appliance.GetFullyQualifiedResourceId()}' from ADB RP successfully.");

                await Task.Delay(WaitTimeInMilliSeconds);

                // jobExecutionResult is Failed when the appliance deletion failed with permanent error
                // In this case, we should log the failure and continue with the next appliance deletion
                if (jobExecutionResult != null && jobExecutionResult.Status == JobExecutionStatus.Failed)
                {
                    this.Logger.LogError("SubscriptionDeleteJobFailure",
                        $"workspace delete notification to databricks failed," +
                        $"ResourceId: {appliance.GetFullyQualifiedResourceId()}," +
                        $"SubscriptionId: {appliance.SubscriptionId}," +
                        $"Error message: {jobExecutionResult.Message}");
                }
            }
            return jobExecutionResult;
        }

        private async Task DeleteAppliances(ApplianceEntity appliance)
        {
            try
            {
                await this
                    .applianceDataProvider
                    .DeleteAppliances(new[]
                    {
                        appliance
                    })
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
            catch (StorageException exception)
            {
                // If the exception status code is 404, it's likely caused by a new index
                if (exception.RequestInformation.HttpStatusCode != (int)HttpStatusCode.NotFound)
                {
                    throw;
                }
            }
        }

        /// <summary>
        /// Calls DB for the workspace DELETE notification.
        /// </summary>
        /// <param name="appliance">The appliance.</param>
        /// <param name="subscriptionNotificationPropertiesDefinition">Susbcription Notification.</param>
        /// <param name="registeredFeatures"></param>
        /// <returns>returns the job execution result based on response from databricks.</returns>
        public async Task<JobExecutionResult> SendDeleteWorkspaceNotificationToDatabricks(
            ApplianceEntity appliance,
            SubscriptionNotificationPropertiesDefinition subscriptionNotificationPropertiesDefinition,
            List<string> registeredFeatures)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var customerTenantId = subscriptionNotificationPropertiesDefinition.TenantId;

            try
            {
                var additionalHeaders = new InsensitiveDictionary<string>
                       {
                           {
                            ProviderConstants.DBWorkspaceNotification.DatabricksOnBehalfOfUserHeader, ProviderConstants.DBWorkspaceNotification.DatabricksApplicationId
                           },
                           {
                                   ProviderConstants.DBWorkspaceNotification.SubscriptionDeleteNotificationHeader,
                                   "true"
                           }
                       };

                try
                {
                    // Pass the job metadata to enable job-based polling
                    await DatabricksAccountsManager.NotifyDatabricksForWorkspaceDelete(
                        appliance.SubscriptionId,
                        appliance.GetFullyQualifiedResourceId(),
                        customerTenantId,
                        registeredFeatures,
                        additionalHeaders,
                        this.Metadata).ConfigureAwait(false);

                    // If we reach here, it means the operation completed synchronously
                    return default;
                }
                catch (JobPostponedException ex)
                {
                    // Handle job postponement for Account API polling
                    this.Logger.LogDebug(
                        operationName,
                        $"Job postponed for Account API polling: {ex.Message}");

                    return new JobExecutionResult
                    {
                        Status = JobExecutionStatus.Postponed,
                        NextExecutionTime = DateTime.UtcNow.Add(ex.PostponeDelay),
                        Message = ex.Message,
                        NextMetadata = this.Metadata.ToJson()
                    };
                }
            }
            catch (Exception ex)
            {
                return HandleSubscriptionDeleteJobExeception(appliance.GetFullyQualifiedResourceId(), operationName, ErrorResponseCode.WorkspaceDeleteFailed, ex);
            }
        }

        /// <summary>
        /// Deletes batch of access connector entities from the subscription
        /// </summary>
        /// <param name="subscriptionId"> The subscription id</param>
        /// <param name="tenantId"> The tenant id of the subscription</param>
        /// <param name="registeredFeatures"> List of registered features on subscription</param>
        /// <param name="batchSize">The number of entities to delete</param>
        private async Task<JobExecutionResult> DeleteAccessConnectorsFromSubscription(string subscriptionId,
            string tenantId,
            List<string> registeredFeatures,
            int batchSize = 100)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            JobExecutionResult jobExecutionResult = default;

            var accessConnectorEntities = await this.GetAccessConnectorDataProvider()
                .FindAccessConnectorsSegmented(subscriptionId: subscriptionId, top: batchSize).ConfigureAwait(false);

            if (accessConnectorEntities == null || accessConnectorEntities.Entities.Count() == 0)
            {
                this.Logger.LogDebug(operationName,
                    $"No access connector in SubscriptionId: {subscriptionId} found!.");
                return default;
            }

            this.Logger.LogDebug(operationName,
                        $"Total access connectors {accessConnectorEntities.Entities?.Length} will be deleted from the subscription id {this.Metadata.SubscriptionId} ");

            foreach (var accessConnectorEntity in accessConnectorEntities.Entities)
            {
                try
                {
                    var credMgrNotification = new CredMgrNotification(accessConnectorEntity.GetResourceId(), IdentityType.None, accessConnectorEntity.SubscriptionId, accessConnectorEntity.ResourceGroup, accessConnectorEntity.Name);

                    var additionalHeaders = new InsensitiveDictionary<string>
                       {
                           {
                            ProviderConstants.DBWorkspaceNotification.DatabricksOnBehalfOfUserHeader, ProviderConstants.DBWorkspaceNotification.DatabricksApplicationId
                           }
                       };

                    await DatabricksCredentialManager.NotifyCredentialManager(credMgrNotification, tenantId, registeredFeatures, additionalHeaders);

                    await this.accessConnectorDataProvider.DeleteAccessConnector(accessConnectorEntity);

                    this.Logger.LogDebug(operationName, $"Successfully deleted accessconnetor {accessConnectorEntity.GetResourceId()}" +
                        $" from subsciption: {subscriptionId}.");

                    await Task.Delay(WaitTimeInMilliSeconds);
                }
                catch (Exception ex)
                {
                    // returns job execution result based on the exception
                    jobExecutionResult = HandleSubscriptionDeleteJobExeception(accessConnectorEntity.GetResourceId(), operationName, ErrorResponseCode.AccessConnectorDeletionFailed, ex);

                    if (jobExecutionResult.Status == JobExecutionStatus.Postponed)
                        return jobExecutionResult;
                }

                // jobExecutionResult is Failed when the access connector deletion failed with permanent error
                // In this case, we should log the failure and continue with the next access connector deletion
                if (jobExecutionResult != null && jobExecutionResult.Status == JobExecutionStatus.Failed)
                {
                    try
                    {
                        await this.accessConnectorDataProvider.DeleteAccessConnector(accessConnectorEntity);
                        this.Logger.LogError("SubscriptionDeleteJobFailure",
                                   $"Delete access connector notification to databricks failed ," +
                                   $"ResourceId: {accessConnectorEntity.GetResourceId()}," +
                                   $"SubscriptionId: {accessConnectorEntity.SubscriptionId}" +
                                   $"Error message: {jobExecutionResult.Message}");
                    }
                    catch (Exception ex)
                    {
                        jobExecutionResult.Message = ex.Message;
                        return jobExecutionResult;
                    }
                }
            }

            if (accessConnectorEntities.ContinuationToken != null)
            {
                return this.PostponeJob(message: "Postponing the job to execute the next batch of access connectors",
                                        delay: this.PostponeInterval);
            }

            return jobExecutionResult;
        }

        private JobExecutionResult HandleSubscriptionDeleteJobExeception(string resourceId, string operationName, ErrorResponseCode errorResponseCode, Exception exception)
        {
            this.Logger.LogError(
                 operationName: operationName,
                 format: "Deleting resource type '{0}' with id '{1}' failed with exception '{2}', Retry Attempt '{3}'.",
                 arg0: this.Metadata.ResourceType,
                 arg1: resourceId,
                 arg2: exception.ToString(),
                 arg3: this.Metadata.CurrentRetryableErrorCount);

            if ((exception is ServerErrorResponseMessageException serverErrorResponseMessageException) && serverErrorResponseMessageException.IsOperationExceptionRetryable(
                    this.Metadata.CurrentRetryableErrorCount,
                    this.MaxRetries))
            {
                this.Metadata.CurrentRetryableErrorCount++;
                return this.PostponeJob(
                    message: serverErrorResponseMessageException.Message,
                    delay: TimeSpan.FromTicks(
                        this.PostponeInterval.Ticks * this.Metadata.CurrentRetryableErrorCount));
            }
            else
            {
                this.Logger.LogError(
                    operationName: operationName,
                    $"Subscription delete job failed for subscription ID : {this.Metadata.SubscriptionId} with unretryable exception or max number of retries : {this.Metadata.CurrentRetryableErrorCount} reached . Exception: '{exception}'");

                this.Metadata.OperationResponseStatusCode = HttpStatusCode.OK;
                this.Metadata.OperationResponseContent = AsyncOperationResult
                    .GetAsyncOperationResult(
                        provisioningState: ProvisioningState.Failed,
                        errorCode: errorResponseCode.ToString(),
                        message: exception.Message)
                    .ToJToken();

                return new JobExecutionResult
                {
                    Status = JobExecutionStatus.Failed,
                    Message = $"Subscription deletion job failed.{exception.Message}",
                    NextMetadata = this.Metadata.ToJson()
                };
            }
        }
    }
}