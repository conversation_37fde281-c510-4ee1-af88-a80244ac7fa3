namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities
{
    using System;
    using System.Collections.Generic;
    using System.Net;
    using System.Net.Http;
    using System.Runtime.InteropServices;
    using System.ServiceModel.Syndication;
    using System.Threading.Tasks;
    using System.Web;
    using Microsoft.AspNetCore.JsonPatch.Operations;
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Newtonsoft.Json.Linq;
    using OpenTelemetry.Resources;

    public class DatabricksAccountsManagerUtils
    {
        /// <summary>
        /// Gets the base uri and aad audience using registered features on subscription.
        /// </summary>
        /// <param name="registeredFeatures"></param>
        /// <returns>Return tuple of base uri and aad audience</returns>
        public static (Uri baseUri, string aadAudience, bool UseArm) GetBaseUriAndAadAudience(List<string> registeredFeatures, ICommonEventSource logger)
        {
            var operationName = "GetBaseUriAndAadAudience";
            (Uri baseUri, string aadAudience, bool UseArm) baseUrlAndAudience;

            if (registeredFeatures.Contains(ProviderConstants.Databricks.UseDatabricksAccountApiFeature))
            {
                //Test account api, reverse this for now
                //Change to true when the flag is applied and code ready.
                baseUrlAndAudience.UseArm = false;
            }
            else
            {
                baseUrlAndAudience.UseArm = true;
            }

            if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksDevControlPlaneFeature))
            {
                baseUrlAndAudience.baseUri = GetControlPlaneBaseUrl(DatabricksEnv.Dev, logger);
                baseUrlAndAudience.aadAudience = GetAadAudienceForControlPlane(DatabricksEnv.Dev, logger);
            }
            else if (registeredFeatures.Contains(ProviderConstants.Databricks.MicrosoftDatabricksStagingControlPlaneFeature))
            {
                baseUrlAndAudience.baseUri = GetControlPlaneBaseUrl(DatabricksEnv.Staging, logger);
                baseUrlAndAudience.aadAudience = GetAadAudienceForControlPlane(DatabricksEnv.Staging, logger);
            }
            else
            {
                baseUrlAndAudience.baseUri = GetControlPlaneBaseUrl(DatabricksEnv.Prod, logger);
                baseUrlAndAudience.aadAudience = GetAadAudienceForControlPlane(DatabricksEnv.Prod, logger);
            }

            logger.Debug(operationName, $"baseUrl : {baseUrlAndAudience.baseUri.AbsoluteUri}, audience: {baseUrlAndAudience.aadAudience}");
            return baseUrlAndAudience;
        }

        /// <summary>
        /// Gets the Databricks Login App ID used as Target Audience.
        /// </summary>
        /// <param name="envType">Databricks environment type.</param>
        private static string GetAadAudienceForControlPlane(DatabricksEnv envType, ICommonEventSource logger)
        {
            var operationName = "GetAadAudienceForControlPlane";

            logger.Debug(operationName, $"envType : {envType}");
            var audience = CloudConfigurationManager.GetConfiguration("DatabricksProdEnvironmentLoginAppId",
                ProviderConstants.Databricks.DatabricksProdLogindAppId);

            switch (envType)
            {
                case DatabricksEnv.Dev:
                    audience = CloudConfigurationManager.GetConfiguration("DatabricksDevEnvironmentLoginAppId",
                        ProviderConstants.Databricks.DatabricksDevLogindAppId);
                    break;

                case DatabricksEnv.Staging:
                    audience = CloudConfigurationManager.GetConfiguration("DatabricksStagingEnvironmentLoginAppId",
                       ProviderConstants.Databricks.DatabricksStagingLogindAppId);
                    break;

                case DatabricksEnv.Prod:
                default:
                    break;
            }

            logger.Debug(operationName, $"audience : {audience}");
            return audience;
        }

        /// <summary>
        /// Gets the baseUrl for ControlPlane Credential Manager Service.
        /// </summary>
        /// <param name="envType">Databricks environment type.</param>
        private static Uri GetControlPlaneBaseUrl(DatabricksEnv envType, ICommonEventSource logger)
        {
            var operationName = "GetControlPlaneBaseUrl";

            var baseUrl = CloudConfigurationManager.GetConfiguration("DatabricksAccountsMgrDns",
                ProviderConstants.Databricks.DatabricksAccountsProdServiceUrl);

            switch (envType)
            {
                case DatabricksEnv.Dev:
                    baseUrl = string.Format(
                        CloudConfigurationManager.GetConfiguration("DatabricksAccountsMgrDevStagingDnsTemplate",
                        ProviderConstants.Databricks.DatabricksAccountsNonProdServiceUrl), "dev");
                    break;

                case DatabricksEnv.Staging:
                    baseUrl = string.Format(
                        CloudConfigurationManager.GetConfiguration("DatabricksAccountsMgrDevStagingDnsTemplate",
                        ProviderConstants.Databricks.DatabricksAccountsNonProdServiceUrl), "staging");
                    break;

                case DatabricksEnv.Prod:
                default:
                    break;
            }

            logger.Debug(operationName, $"baseUrl: {baseUrl}, env: {envType}");
            return new Uri(baseUrl);
        }
        /// <summary>
        /// Get DB Workspace
        /// </summary>
        /// <typeparam name="T">WorkspaceDetails for ARM, and DBAsyncWorkspace for AccountApi</typeparam>
        /// <param name="engine">Frontdoor Engine</param>
        /// <param name="customerTenantId">Customer Tenant Id</param>
        /// <param name="workspaceId">Workspace Id</param>
        /// <param name="baseUri">Base URL</param>
        /// <param name="apiVersion">Api Version</param>
        /// <param name="audience">AAD Audience</param>
        /// <param name="useArm">Use ARM or not</param>
        /// <param name="additionalHeaders">Additional Headers</param>
        /// <returns></returns>
        public static async Task<T> GetDBWorkspace<T>(
            IFrontdoorEngine engine,
            string customerTenantId,
            string workspaceId,
            Uri baseUri,
            string apiVersion,
            string audience,
            bool useArm,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            Uri workspaceRequestUri = null;
            if (useArm)
            {
                workspaceRequestUri = UriTemplateEngine.WorkspaceInitializationOperationUri(
                    engine.FrontdoorEndpointUri,
                    workspaceId,
                    apiVersion);
            }
            else
            {
                workspaceRequestUri = UriTemplateEngine.DBWorkspaceOperationUri(
                    baseUri,
                    customerTenantId,
                    workspaceId.TrimStart('/'));
            }
            try
            {
                var response = await engine
                .GetDbWorkspaceDetailsAsync<T>(
                    workspaceRequestUri,
                    apiVersion,
                    ProviderConstants.Databricks.ResourceProviderNamespace,
                    audience,
                    engine.WorkspaceInitializeRetryCount,
                    engine.WorkspaceInitializeRetryInterval)
                .ConfigureAwait(false);
                return response;
            }
            catch (Exception ex)
            {
                throw;
            }


        }
        public static async Task SendDBNotificationWithFlagControl(
            IFrontdoorEngine engine,
            JToken payload,
            HttpMethod method,
            string customerTenantId,
            bool useArm,
            string workspaceId,
            Uri baseUri,
            string apiVersion,
            string audience,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            var operationName = "SendDBNotificationWithFlagControl";

            Uri workspacePatchRequestUri = null;
            if (useArm)
            {
                workspacePatchRequestUri = UriTemplateEngine.WorkspaceInitializationOperationUri(
                    engine.FrontdoorEndpointUri,
                    workspaceId,
                    apiVersion);
            }
            else
            {
                workspacePatchRequestUri = UriTemplateEngine.DBWorkspaceOperationUri(
                    baseUri,
                    customerTenantId,
                    workspaceId.TrimStart('/'));
            }
            await engine
                .DBWorkspaceNotificationWrapper(
                method,
                workspacePatchRequestUri,
                ProviderConstants.Databricks.ResourceProviderNamespace,
                customerTenantId,
                workspaceId,
                payload,
                audience,
                useArm,
                additionalHeaders)
                .ConfigureAwait(false);
        }

        public static async Task<WorkspaceUpdateResponse> SendDBUpdateNotificationWithFlagControl(
            IFrontdoorEngine engine,
            JToken payload,
            HttpMethod method,
            string customerTenantId,
            bool useArm,
            string workspaceId,
            Uri baseUri,
            string apiVersion,
            string audience,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            var operationName = "SendDBUpdateNotificationWithFlagControl";

            Uri workspacePatchRequestUri = null;
            if (useArm)
            {
                workspacePatchRequestUri = UriTemplateEngine.WorkspaceInitializationOperationUri(
                    engine.FrontdoorEndpointUri,
                    workspaceId,
                    apiVersion);
            }
            else
            {
                workspacePatchRequestUri = UriTemplateEngine.DBWorkspaceOperationUri(
                    baseUri,
                    customerTenantId,
                    workspaceId.TrimStart('/'));
            }

            return await engine
                .DBWorkspaceUpdateNotificationWrapper(
                method,
                workspacePatchRequestUri,
                ProviderConstants.Databricks.ResourceProviderNamespace,
                customerTenantId,
                workspaceId,
                payload,
                audience,
                useArm,
                baseUri,
                additionalHeaders)
                .ConfigureAwait(false);
        }


    }
}
