﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses
{
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using System;

    /// <summary>
    /// The workspace initialization operation response
    /// </summary>
    public class WorkspaceInitializeResponse : OperationResponse
    {
        /// <summary>
        /// Gets or sets the workspace details
        /// </summary>
        public WorkspaceDetails WorkspaceDetails { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether polling is required for this operation
        /// </summary>
        public bool RequiresPolling { get; set; }

        /// <summary>
        /// Gets or sets the polling state when RequiresPolling is true
        /// </summary>
        public AccountApiPollingState PollingState { get; set; }

        /// <summary>
        /// Gets or sets the operation ID to use for polling
        /// </summary>
        public string OperationId { get; set; }

        /// <summary>
        /// Gets or sets the base URI for the Account API
        /// </summary>
        public Uri BaseUri { get; set; }

        /// <summary>
        /// Gets or sets the audience for the Account API
        /// </summary>
        public string Audience { get; set; }

        /// <summary>
        /// Gets or sets the polling interval in seconds
        /// </summary>
        public int PollingIntervalSeconds { get; set; }

        /// <summary>
        /// Gets or sets the resource operation for polling
        /// </summary>
        public ProvisioningOperation ResourceOperation { get; set; }
    }

    /// <summary>
    /// The workspace update operation response
    /// </summary>
    public class WorkspaceUpdateResponse : OperationResponse
    {
        /// <summary>
        /// Gets or sets a value indicating whether polling is required for this operation
        /// </summary>
        public bool RequiresPolling { get; set; }

        /// <summary>
        /// Gets or sets the polling state when RequiresPolling is true
        /// </summary>
        public AccountApiPollingState PollingState { get; set; }

        /// <summary>
        /// Gets or sets the operation ID to use for polling
        /// </summary>
        public string OperationId { get; set; }

        /// <summary>
        /// Gets or sets the base URI for the Account API
        /// </summary>
        public Uri BaseUri { get; set; }

        /// <summary>
        /// Gets or sets the audience for the Account API
        /// </summary>
        public string Audience { get; set; }

        /// <summary>
        /// Gets or sets the polling interval in seconds
        /// </summary>
        public int PollingIntervalSeconds { get; set; }

        /// <summary>
        /// Gets or sets the resource operation for polling
        /// </summary>
        public ProvisioningOperation ResourceOperation { get; set; }
    }
}