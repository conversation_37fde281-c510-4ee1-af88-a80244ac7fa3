﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines
{
    using System;
    using System.Collections.Generic;
    using System.Collections.Specialized;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Net.Http.Formatting;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading;
    using System.Threading.Tasks;
    using global::Azure;
    using Microsoft.Identity.Client;
    using Microsoft.WindowsAzure.ResourceStack.Common.Algorithms;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.EventSources;
    using Microsoft.WindowsAzure.ResourceStack.Common.Exceptions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Common.Services;
    using Microsoft.WindowsAzure.ResourceStack.Common.Storage;
    using Microsoft.WindowsAzure.ResourceStack.Common.Utilities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Connectors;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.Deployments;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.SecurityRule;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Utilities;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using HttpResponseHeaders = System.Net.Http.Headers.HttpResponseHeaders;
    using JsonExtensions = Extensions.JsonExtensions;

    /// <summary>
    /// The front door engine.
    /// </summary>
    public class FrontdoorEngine : IFrontdoorEngine
    {
        #region Properties and constructors

        /// <summary>
        /// The prepare or un prepare polling interval.
        /// </summary>
        private const int PreparePollingIntervalSeconds = 2;

        /// <summary>
        /// The front door API version.
        /// </summary>
        private static readonly string FrontdoorApiVersion = "2018-07-01";

        /// <summary>
        /// The API version of the role definition/assignments resource type.
        /// </summary>
        private static readonly string AuthorizationApiVersion = "2022-04-01";

        /// <summary>
        /// The API version of the security rule resource type.
        /// </summary>
        private static readonly string SecurityRuleApiVersion = "2024-07-01";

        /// <summary>
        /// Regex to parse authorization failure error
        /// </summary>
        private static readonly Regex WwwAuthenticateHeaderRegex = new Regex("authorization_uri=\"https://(.*)/(.*?)\"", RegexOptions.IgnoreCase);

        // Retry count for the call to ARM metadata endpoint.
        private const int ArmMetadataRetryCount = 5;

        // Time to retry after, in seconds
        private const int ArmMetadataRetryInterval = 5;

        /// <summary>
        /// Gets or sets the event source.
        /// </summary>
        private ICommonEventSource EventSource { get; set; }

        private readonly ICacheProvidersContainer _cacheProvidersContainer;

        /// <summary>
        /// Gets or sets the cache providers container.
        /// </summary>
        public ICacheProvidersContainer CacheProvidersContainer => _cacheProvidersContainer;

        /// <summary>
        /// Gets or sets the MediaTypeFormatter
        /// </summary>
        private MediaTypeFormatter MediaTypeFormatter { get; set; }

        /// <summary>
        /// Gets or sets the front door client.
        /// </summary>
        private FrontdoorClient FrontdoorClient { get; set; }

        /// <summary>
        /// Gets a value of the prepare or un-prepare <c>api</c> version
        /// </summary>
        private static string VirtualNetworkInjectionOperationsApiVersionKey
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(ProviderConstants.Databricks.VirtualNetworkInjectionOperationsApiVersionKey);
            }
        }

        // Key used to retrieve the ARM metadata endpoint from the config.
        private const string ArmMetadataEndpointConfig = "Microsoft.WindowsAzure.ResourceStack.Providers.ArmMetadataEndpoint";

        /// <summary>
        /// Gets a value indicating whether the environment is development.
        /// </summary>
        private Uri ArmMetadataEndpoint
        {
            get
            {
                return new Uri(CloudConfigurationManager.GetConfiguration(ArmMetadataEndpointConfig));
            }
        }

        /// <summary>
        /// Gets a value of the Machine Learning Workspace <c>api-version</c>
        /// </summary>
        private static string AmlWorkspaceOperationsApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(ProviderConstants.Databricks.AmlWorkspaceApiVersion);
            }
        }

        /// <summary>
        /// Gets a value of the PAS deny assignments <c>api</c> version.
        /// </summary>
        private static string DenyAssignmentsApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration("DenyAssignmentsApiVersion");
            }
        }

        /// <summary>
        /// Gets a value of the features <c>api</c> version.
        /// </summary>
        private static string FeaturesApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration("FeaturesApiVersion");
            }
        }

        /// <summary>
        /// Gets the identity service request retry count.
        /// </summary>
        private static int IdentityServiceRetryCount
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationNumber("Microsoft.WindowsAzure.ResourceStack.Providers.Identity.RequestRetryCount", 3);
            }
        }

        /// <summary>
        /// Gets the identity service request retry interval.
        /// </summary>
        private static TimeSpan IdentityServiceRetryInterval
        {
            get
            {
                return CloudConfigurationManager.GetConfigurationTimeSpan("Microsoft.WindowsAzure.ResourceStack.Providers.Identity.RequestRetryInterval", TimeSpan.FromSeconds(1));
            }
        }

        /// <summary>
        /// Gets the managed identity credential API version.
        /// </summary>
        /// <value>
        /// The managed identity credential API version.
        /// </value>
        public static string ManagedIdentityControlPlaneApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(ProviderConstants.ManagedIdentity.ManagedIdentityControlPlaneApiVersionKey,
                    ProviderConstants.ManagedIdentity.DefaultManagedIdentityControlPlaneApiVersion);
            }
        }

        /// <summary>
        /// Gets the managed identity ARM API version.
        /// </summary>
        /// <value>
        /// The managed identity ARM API version.
        /// </value>
        public static string ManagedIdentityDataPlaneApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(ProviderConstants.ManagedIdentity.ManagedIdentityDataPlaneApiVersionKey,
                    ProviderConstants.ManagedIdentity.DefaultManagedIdentityDataPlaneApiVersion);
            }
        }

        /// <summary>
        /// Gets the managed identity audience.
        /// </summary>
        /// <value>
        /// The managed identity ARM API version.
        /// </value>
        public static string ManagedIdentityDataPlaneAudience
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration(ProviderConstants.ManagedIdentity.ManagedIdentityDataPlaneAudienceKey,
                    ProviderConstants.ManagedIdentity.DefaultManagedIdentityDataPlaneAudience);
            }
        }

        /// <summary>
        /// Gets the maximum retries.
        /// </summary>
        private static int PrivateLinkMaxRetries =>
            CloudConfigurationManager.GetConfigurationNumber(ProviderConstants.Databricks.PrivateLinkMaxRetriesKey, 3);

        /// <summary>
        /// Gets the retry timeout for Graph calls.
        /// </summary>
        private static TimeSpan GraphApiRetryTimeout
        {
            get => CloudConfigurationManager.GetConfigurationTimeSpan(
                settingName: "Microsoft.WindowsAzure.ResourceStack.Frontdoor.Graph.RetryTimeout",
                defaultValue: TimeSpan.FromSeconds(60));
        }

        /// <summary>
        /// Gets the default retry after interval for private link operations.
        /// </summary>
        private static TimeSpan PrivateLinkDefaultRetryInterval =>
            CloudConfigurationManager.GetConfigurationTimeSpan(
                ProviderConstants.Databricks.RetryIntervalSetting,
                TimeSpan.FromSeconds(15));

        /// <summary>
        /// Gets the Workspace Notification Retry Count
        /// </summary>
        private static int DBWorkspaceNotificationRetryCount
        {
            get
            {
                return CloudConfigurationManager
                    .GetConfigurationNumber(ProviderConstants.DBWorkspaceNotification.DBWorkspaceNotificationRetryCount, 3);
            }
        }

        /// <summary>
        /// Gets the Workspace Notification Retry Interval in seconds
        /// </summary>
        private static int DBWorkspaceNotificationRetryIntervalSecs
        {
            get
            {
                return CloudConfigurationManager
                    .GetConfigurationNumber(ProviderConstants.DBWorkspaceNotification.DBWorkspaceNotificationRetryIntervalSecsKey, 5);
            }
        }

        /// <summary>
        /// Gets the storage update API version.
        /// </summary>
        /// <value>
        /// The storage update API version.
        /// </value>
        public string StorageApiVersion
        {
            get
            {
                return CloudConfigurationManager.GetConfiguration("StorageApiVersion");
            }
        }

        /// <summary>
        /// Gets the front door endpoint URI.
        /// </summary>
        public Uri FrontdoorEndpointUri
        {
            get
            {
                return new Uri(CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.FrontdoorEndpointUri"));
            }
        }

        /// <summary>
        /// Gets the Workspace Initialize Retry Interval in seconds
        /// </summary>
        public int WorkspaceInitializeRetryInterval
        {
            get
            {
                return CloudConfigurationManager
                    .GetConfigurationNumber(ProviderConstants.Databricks.WorkspaceInitializationRetryIntervalKey, 2);
            }
        }

        /// <summary>
        /// Gets the Workspace polling retry count
        /// </summary>
        public int AccountApiPollingRetryCount
        {
            get
            {
                return CloudConfigurationManager
                    .GetConfigurationNumber(ProviderConstants.Databricks.AccountApiPollingRetryCountKey, 120);
            }
        }

        /// <summary>
        /// Gets the Workspace account api Retry Interval in seconds
        /// </summary>
        public int AccountApiPollingRetryInterval
        {
            get
            {
                return CloudConfigurationManager
                    .GetConfigurationNumber(ProviderConstants.Databricks.AccountApiPollingRetryIntervalKey, 30);
            }
        }

        /// <summary>
        /// Gets the Workspace Initialize Retry Interval in seconds
        /// </summary>
        public int WorkspaceInitializeRetryCount
        {
            get
            {
                return CloudConfigurationManager
                    .GetConfigurationNumber(ProviderConstants.Databricks.WorkspaceInitializationRetryCount, 3);
            }
        }

        /// <summary>
        /// Gets the additional header to indicate to PAS not to return cached role assignments on GET.
        /// </summary>
        public static KeyValuePair<string, string>[] RoleAssignmentNoCacheHeader
        {
            get
            {
                return new[]
                {
                    new KeyValuePair<string, string>(ProviderConstants.Databricks.RoleAssignmentNoCacheHeader, "true")
                };
            }
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="FrontdoorEngine" /> class.
        /// </summary>
        /// <param name="eventSource">The event source.</param>
        /// <param name="cacheProvidersContainer">The cache providers container.</param>
        public FrontdoorEngine(
            ICommonEventSource eventSource,
            ICacheProvidersContainer cacheProvidersContainer)
        {
            this.EventSource = eventSource;
            this._cacheProvidersContainer = cacheProvidersContainer;
            this.MediaTypeFormatter = JsonExtensions.JsonMediaTypeFormatter;
            this.FrontdoorClient = new FrontdoorClient();
        }

        #endregion

        #region subscription

        /// <summary>
        /// Get Resource Home Tenant ID esp. scenario manual private link approval for cross tenant resources.
        /// </summary>
        /// <param name="requestUri"></param>
        /// <param name="resourceProviderNamespace"></param>
        /// <param name="homeTenantId"></param>
        /// <returns></returns>
        public async Task<string> GetTenantIdAsync(
            Uri requestUri,
            string resourceProviderNamespace,
            string homeTenantId)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName,
                $"[Uri:{HttpMethod.Get} {requestUri.AbsoluteUri}] using Tenant ID :{homeTenantId}]");

            try
            {
                var authenticationToken = await this
                    .GetAuthenticationTokenFromCache(
                        homeTenantId)
                    .ConfigureAwait(false);

                using (var response = await this.FrontdoorClient
                    .CallFrontdoorService(
                        HttpMethod.Get,
                        requestUri,
                        authenticationToken.AccessToken)
                    .ConfigureAwait(false))
                {
                    if (response?.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        var authenticationHeaderValue = response.Headers.WwwAuthenticate
                            .FirstOrDefault();
                        /*
                            * Whenever GET call hits 401 unauthorized error for our first party app, then we need parse HTTP Response authorization_uri and get the tenantId.
                            * Http Response look like
                            * 401 Unauthorized
                            * WWW-Authenticate: Bearer authorization_uri=""https://login.windows.net/72f988bf-86f1-41af-91ab-2d7cd011db47"", error=""invalid_token"",
                            * error_description=""The access token is from the wrong issuer. It must match the tenant associated with this subscription.
                            * Please use correct authority to get the token.""", tenantId
                        */

                        if (authenticationHeaderValue != null && response.Headers.WwwAuthenticate != null)
                        {
                            var value = authenticationHeaderValue.ToString();
                            var match = WwwAuthenticateHeaderRegex.Match(value);

                            if (match.Success)
                            {
                                var privateEndpointTenantId = match.Groups[2]
                                    .Value;
                                this.EventSource.Debug(operationName,
                                    $"[Uri:{HttpMethod.Get} {requestUri.AbsoluteUri}] using Tenant ID :{privateEndpointTenantId}]");

                                return privateEndpointTenantId;
                            }

                            this.EventSource.Debug(
                                operationName,
                                $"Fetching the customer's home tenant id for resource {requestUri.AbsoluteUri} got failed.The www-authenticate header is empty or invalid format: {value}");

                            throw new ServerErrorResponseMessageException(
                                HttpStatusCode.InternalServerError,
                                ErrorResponseCode.InternalServerError.ToString(),
                                ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext
                                    .Current.CorrelationId));
                        }
                    }
                }
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"The '{requestUri}' lookup using tenant '{homeTenantId}' is failed. Exception: {Utilities.FlattenException(exception)} & ResponseCode: {exception.HttpStatus}");
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Unable to fetch tenant id for '{requestUri}'. Exception: {Utilities.FlattenException(exception)}");
            }

            return homeTenantId;
        }

        /// <summary>
        /// Get Resource Home Tenant ID
        /// </summary>
        /// <param name="requestUri"></param>
        /// <returns>Customer tenant id</returns>
        public async Task<string> GetCustomerTenantIdAsync(
            Uri requestUri)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            string customerTenantId = string.Empty;

            try
            {
                using (var response = await this.FrontdoorClient
                    .CallFrontdoorService(
                        HttpMethod.Get,
                        requestUri,
                        string.Empty)
                    .ConfigureAwait(false))
                {
                    if (response?.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        var authenticationHeaderValue = response.Headers.WwwAuthenticate
                            .FirstOrDefault();
                        /*
                            * Whenever GET call hits 401 unauthorized error for our first party app, then we need parse HTTP Response authorization_uri and get the tenantId.
                            * Http Response look like
                            * 401 Unauthorized
                            * WWW-Authenticate: Bearer authorization_uri=""https://login.windows.net/72f988bf-86f1-41af-91ab-2d7cd011db47"", error=""invalid_token"",
                            * error_description=""The access token is from the wrong issuer. It must match the tenant associated with this subscription.
                            * Please use correct authority to get the token.""", tenantId
                        */

                        if (authenticationHeaderValue != null && response.Headers.WwwAuthenticate != null)
                        {
                            var value = authenticationHeaderValue.ToString();
                            var match = WwwAuthenticateHeaderRegex.Match(value);

                            if (match.Success)
                            {
                                customerTenantId = match.Groups[2]
                                  .Value;
                                this.EventSource.Debug(operationName,
                                    $"[Uri:{HttpMethod.Get} {requestUri.AbsoluteUri}] using Tenant ID :{customerTenantId}]");

                                return customerTenantId;
                            }

                            this.EventSource.Debug(
                                operationName,
                                $"Fetching the customer's home tenant id for resource {requestUri.AbsoluteUri} got failed.The www-authenticate header is empty or invalid format: {value}");

                            throw new ServerErrorResponseMessageException(
                                HttpStatusCode.InternalServerError,
                                ErrorResponseCode.InternalServerError.ToString(),
                                ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext
                                    .Current.CorrelationId));
                        }
                    }
                    else if (response?.StatusCode == HttpStatusCode.NotFound)
                    {
                        throw new Exception($"{requestUri.AbsoluteUri} does not exists");
                    }
                    else
                    {
                        this.EventSource.Error(
                                            operationName,
                                            $"The '{requestUri.AbsoluteUri}' lookup for customer tenant id is failed. Statuscode: {response?.StatusCode}");
                    }
                }
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"The '{requestUri.AbsoluteUri}' lookup for customer tenant id is failed. Exception: {Utilities.FlattenException(exception)} & ResponseCode: {exception.HttpStatus}");
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Unable to fetch customer tenant for '{requestUri.AbsoluteUri}'. Exception: {Utilities.FlattenException(exception)}");
                throw;
            }

            return customerTenantId;
        }

        #endregion

        #region Resource groups

        /// <summary>
        /// Gets a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="expand">The expand query to get more properties</param>
        public async Task<ResourceGroupDefinition> GetResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace,
            string expand = null)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var resourceGroupUri = expand == null
                ? UriTemplateEngine.GetResourceGroupRequestUri(
                    endpoint: this.FrontdoorEndpointUri,
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    apiVersion: FrontdoorEngine.FrontdoorApiVersion)
                : UriTemplateEngine.GetResourceGroupRequestUri(
                    endpoint: this.FrontdoorEndpointUri,
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    apiVersion: FrontdoorEngine.FrontdoorApiVersion,
                    expand: expand);

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await AsyncRetry.Retry(
                           () => this.FrontdoorClient
                               .CallFrontdoorService(
                                   method: HttpMethod.Get,
                                   requestUri: resourceGroupUri,
                                   accessToken: authenticationToken.AccessToken),
                           ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                           TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                           isRetryable: ex =>
                               ex.IsConnectivityException() ||
                               (ex is ServerErrorResponseMessageException exception &&
                                exception.HttpStatus.IsRetryableResponse()),
                           errorAction: (Exception exception) =>
                           {
                               this.EventSource.Debug(operationName,
                                   $"Attempt to GET '{resourceGroupUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                           })
                       .ConfigureAwait(false))
            {
                if (response.StatusCode.IsSuccessfulRequest())
                {
                    return await response.Content
                        .ReadAsJsonAsync<ResourceGroupDefinition>(MediaTypeFormatter)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }
                else if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    return null;
                }

                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (errorResponseMessage != null)
                {
                    throw new ResourceGroupOperationException(
                        httpStatus: response.StatusCode,
                        errorCode: errorResponseMessage.Error.Code,
                        errorMessage: errorResponseMessage.Error.Message);
                }
                else
                {
                    throw new ResourceGroupOperationException(
                        httpStatus: HttpStatusCode.InternalServerError,
                        errorCode: ErrorResponseCode.InternalServerError.ToString(),
                        errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                            RequestCorrelationContext.Current.CorrelationId));
                }
            }
        }

        /// <summary>
        /// Creates a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceGroupDefinition">The resource group definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            ResourceGroupDefinition resourceGroupDefinition,
            string resourceProviderNamespace)
        {
            await this
                .CreateOrUpdateResourceGroup(
                    authenticationTenantId: authenticationTenantId,
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    resourceGroupDefinition: resourceGroupDefinition,
                    resourceProviderNamespace: resourceProviderNamespace,
                    method: HttpMethod.Put)
                .ConfigureAwait(continueOnCapturedContext: false);
        }

        /// <summary>
        /// Updates a resource group
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceGroupDefinition">The resource group definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task UpdateResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            ResourceGroupDefinition resourceGroupDefinition,
            string resourceProviderNamespace)
        {
            await this
                .CreateOrUpdateResourceGroup(
                    authenticationTenantId: authenticationTenantId,
                    subscriptionId: subscriptionId,
                    resourceGroupName: resourceGroupName,
                    resourceGroupDefinition: resourceGroupDefinition,
                    resourceProviderNamespace: resourceProviderNamespace,
                    method: new HttpMethod("PATCH"))
                .ConfigureAwait(continueOnCapturedContext: false);
        }

        /// <summary>
        /// Appends to the tags of the resources in a resource group. Also removes tags specified by tags to remove.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="resource">The resource definition.</param>
        /// <param name="tags">The new tags for the resource.</param>
        public async Task UpdateManagedResourceTags(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace,
            ARMResourceDefinition resource,
            InsensitiveDictionary<string> tags)
        {
            var tagsJsonData = TagUtils.GetTagsParameterForInitialResources(tags);

            var tagsDataJObject = new JObject()
                {
                    new JProperty(ProviderConstants.WorkspaceUpdateParameters.Tags, tagsJsonData["value"])
                };

            this.EventSource.Debug(
                operationName: "FrontdoorEngine.UpdateManagedResourceTags",
                format: "Updating Tags for resource type:{0}.",
                arg0: resource.Type);

            var resourceUri = UriTemplateEngine.GetPatchResourceUri(
                endpoint: this.FrontdoorEndpointUri,
                resourceId: resource.Id,
                apiVersion: Utilities.GetResourceApiVersion(resource.Type));

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: new HttpMethod("PATCH"),
                    requestUri: resourceUri,
                    requestBody: tagsDataJObject,
                    accessToken: authenticationToken.AccessToken,
                    userAgent: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.EventSource.Error(
                        operationName: "FrontdoorEngine.UpdateResourceTags",
                        format: "Patch request to update resource tags for resource id: {0} failed with: {1}",
                        arg0: resource.Id,
                        arg1: errorResponseMessage != null ? errorResponseMessage.Error.Message : string.Empty);

                    if (errorResponseMessage != null)
                    {
                        throw new ResourceGroupOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new ResourceGroupOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }
                }
            }
        }

        /// <summary>
        /// Create or update a resource group, send http request accordingly.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceGroupDefinition">The resource group definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="method">Http method for the request. Use "PUT" for create, "PATCH" for update.</param>
        private async Task CreateOrUpdateResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            ResourceGroupDefinition resourceGroupDefinition,
            string resourceProviderNamespace,
            HttpMethod method)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName,
                format: "Creating or Updating ResourceGroup with name:{0} in subscription: {1} and authenticationTenantId: {2}. Resource Group Definition: {3}",
                arg0: resourceGroupName,
                arg1: subscriptionId,
                arg2: authenticationTenantId,
                arg3: resourceGroupDefinition.ToJson());

            var resourceGroupUri = UriTemplateEngine.GetResourceGroupRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion);

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: method,
                    requestUri: resourceGroupUri,
                    requestBody: resourceGroupDefinition,
                    accessToken: authenticationToken.AccessToken,
                    userAgent: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.EventSource.Debug(operationName,
                        format: "IsSuccessfulRequest: {0}. Error Code: {1}. Error Message: {2}",
                        arg0: response.StatusCode.IsSuccessfulRequest(),
                        arg1: errorResponseMessage?.Error?.Code,
                        arg2: errorResponseMessage?.Error?.Message);

                    if (errorResponseMessage != null)
                    {
                        throw new ResourceGroupOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new ResourceGroupOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }
                }
                else
                {
                    this.EventSource.Debug(operationName,
                        format: "Successful Creation of Resource Group with RequestMessage: '{0}'",
                        arg0: await response.Content.ReadAsStringAsync().ConfigureAwait(false));
                }
            }
        }

        /// <summary>
        /// Deletes a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public Task<Uri> DeleteResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace)
        {
            var resourceGroupUri = UriTemplateEngine.GetResourceGroupRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion);

            return this.ExecuteDeleteResourceOperation<JObject>(
                authenticationTenantId: authenticationTenantId,
                httpMethod: HttpMethod.Delete,
                requestUri: resourceGroupUri,
                resourceProviderNamespace: resourceProviderNamespace,
                requestBody: null);
        }

        /// <summary>
        /// Execute delete resource group operation.
        /// </summary>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="httpMethod">The HTTP method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        private async Task<Uri> ExecuteDeleteResourceOperation<T>(
            string authenticationTenantId,
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            T requestBody)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await AsyncRetry.Retry(
                   () => requestBody != null ?

                        this.FrontdoorClient.CallFrontdoorService<T>(
                            method: httpMethod,
                            requestUri: requestUri,
                            requestBody: requestBody,
                            accessToken: authenticationToken.AccessToken) :

                       this.FrontdoorClient.CallFrontdoorService(
                            method: httpMethod,
                            requestUri: requestUri,
                            accessToken: authenticationToken.AccessToken),

                   ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                   TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                   isRetryable: ex =>
                       ex.IsConnectivityException() ||
                       (ex is ServerErrorResponseMessageException exception &&
                        exception.HttpStatus.IsRetryableResponse()),
                   errorAction: (Exception exception) =>
                   {
                       this.EventSource.Debug(operationName,
                           $"Attempt to {httpMethod} '{requestUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                   })
                .ConfigureAwait(false))
            {
                if (response.StatusCode == HttpStatusCode.Accepted)
                {
                    return response.Headers.Location;
                }
                else if (response.StatusCode.IsSuccessfulRequest() || response.StatusCode == HttpStatusCode.NotFound)
                {
                    return null;
                }

                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(continueOnCapturedContext: false);

                this.EventSource.Debug(
                        operationName: operationName,
                        format: "response status for request uri: {0} is {1}",
                        arg0: requestUri,
                        arg1: response.StatusCode);

                if (errorResponseMessage != null)
                {
                    throw new ResourceGroupOperationException(
                        httpStatus: response.StatusCode,
                        errorCode: errorResponseMessage.Error.Code,
                        errorMessage: errorResponseMessage.Error.Message);
                }
                else
                {
                    throw new ResourceGroupOperationException(
                        httpStatus: HttpStatusCode.InternalServerError,
                        errorCode: ErrorResponseCode.InternalServerError.ToString(),
                        errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                }
            }
        }

        /// <summary>
        /// Gets resource group resources.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="filter">The OData filter condition</param>
        /// <param name="top">Number of resources to be returned or null to return all of them</param>
        public async Task<ARMResourceDefinition[]> GetResourceGroupResources(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace,
            string filter,
            string top)
        {
            var resourceGroupResourcesUri = UriTemplateEngine.GetResourceGroupResourcesRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion,
                filter: filter,
                top: top);

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            var ARMResourceDefinitionList = EmptyArray<ARMResourceDefinition>.Instance;
            bool nextLink;

            do
            {
                using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Get,
                    requestUri: resourceGroupResourcesUri,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false))
                {
                    if (response.StatusCode.IsSuccessfulRequest())
                    {
                        var resourceDefinitions = await response.Content
                            .ReadAsJsonAsync<ResponseWithContinuation<ARMResourceDefinition[]>>(MediaTypeFormatter)
                            .ConfigureAwait(continueOnCapturedContext: false);

                        ARMResourceDefinitionList = ARMResourceDefinitionList.ConcatArray(resourceDefinitions.Value);
                        if (resourceDefinitions.NextLink != null)
                        {
                            nextLink = true;
                            resourceGroupResourcesUri = new Uri(resourceDefinitions.NextLink);
                        }
                        else
                            nextLink = false;
                    }
                    else if (response.StatusCode == HttpStatusCode.NotFound)
                    {
                        return EmptyArray<ARMResourceDefinition>.Instance;
                    }
                    else
                    {

                        var errorResponseMessage = await this
                            .GetErrorResponseMessage(response)
                            .ConfigureAwait(continueOnCapturedContext: false);

                        if (errorResponseMessage != null)
                        {
                            throw new ResourceGroupOperationException(
                                httpStatus: response.StatusCode,
                                errorCode: errorResponseMessage.Error.Code,
                                errorMessage: errorResponseMessage.Error.Message);
                        }
                        else
                        {
                            throw new ResourceGroupOperationException(
                                httpStatus: HttpStatusCode.InternalServerError,
                                errorCode: ErrorResponseCode.InternalServerError.ToString(),
                                errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                        }
                    }
                }
            }
            while (nextLink);

            return ARMResourceDefinitionList;
        }

        #endregion

        #region Authorization

        /// <summary>
        /// Creates a role assignment if one does not already exist for a given principal/role definition pair.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope on which role assignment is to be created.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="roleAssignmentDefinition">The role assignment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateRoleAssignmentIfNotExists(
            string authenticationTenantId,
            string scope,
            string principalId,
            string roleDefinitionId,
            RoleAssignmentDefinition roleAssignmentDefinition,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(
                operationName,
                $"Create role assignments if they don't exist for Principal: {principalId} " +
                $"in resourceProviderNamespace '{resourceProviderNamespace}' with Scope: {scope}" +
                $" and authenticationTenantId '{authenticationTenantId}'. RoleDefinitionId = {roleDefinitionId}");

            var existingRoleAssignment = await this
                .GetRoleAssignments(
                    authenticationTenantId: authenticationTenantId,
                    scope: scope,
                    principalId: principalId,
                    roleDefinitionId: roleDefinitionId,
                    resourceProviderNamespace: resourceProviderNamespace,
                    additionalHeaders: FrontdoorEngine.RoleAssignmentNoCacheHeader)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (!existingRoleAssignment.CoalesceEnumerable().Any())
            {
                this.EventSource.Debug(
                    operationName,
                    $"No existing Role Assignments for Principal: {principalId} in " +
                    $"resourceProviderNamespace '{resourceProviderNamespace}' with Scope: {scope}" +
                    $" and authenticationTenantId '{authenticationTenantId}'. RoleDefinitionId = {roleDefinitionId}");

                var roleAssignmentRequestUri = UriTemplateEngine.GetRoleAssignmentRequestUri(
                    endpoint: this.FrontdoorEndpointUri,
                    scope: scope,
                    roleAssignmentId: Guid.NewGuid().ToString(),
                    apiVersion: FrontdoorEngine.AuthorizationApiVersion);

                await this
                    .CreateAuthorizationResource<RoleAssignmentDefinition>(
                        authenticationTenantId: authenticationTenantId,
                        requestUri: roleAssignmentRequestUri,
                        authorizationResourceDefinition: roleAssignmentDefinition,
                        resourceProviderNamespace: resourceProviderNamespace)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
        }

        /// <summary>
        /// Deletes role assignments if exist for a given principal/role definition pair.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope on which role assignment is to be created.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task DeleteRoleAssignmentIfExists(
            string authenticationTenantId,
            string scope,
            string principalId,
            string roleDefinitionId,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(
                operationName,
                $"Delete role assignments if exist for Principal: '{principalId}' with " +
                $"Scope: '{scope}' and RoleDefinitionId = '{roleDefinitionId}'");

            var existingRoleAssignments = await this
                .GetRoleAssignments(
                    authenticationTenantId,
                    scope,
                    principalId,
                    roleDefinitionId,
                    resourceProviderNamespace,
                    FrontdoorEngine.RoleAssignmentNoCacheHeader)
                .ConfigureAwait(false);

            if (existingRoleAssignments.CoalesceEnumerable().Any())
            {
                this.EventSource.Debug(
                    operationName,
                    $"Found existing Role Assignments for Principal: {principalId} with Scope: {scope} and RoleDefinitionId: '{roleDefinitionId}'");

                //Limiting concurrency to avoid concurrent operations at same scope causing race conditions.
                await existingRoleAssignments.ExecuteOperationsConcurrently(
                        async RoleAssignment =>
                        {
                            this.EventSource.Debug(
                                operationName,
                                $"Deleting Role Assignment Id {RoleAssignment.Id} at scope {scope}");

                            var deleteRoleAssignmentUri = UriTemplateEngine.DeleteRoleAssignmentByIdUri(
                                    this.FrontdoorEndpointUri,
                                    RoleAssignment.Id,
                                    FrontdoorEngine.AuthorizationApiVersion);

                            await this.DeleteAuthorizationResource(
                                authenticationTenantId,
                                deleteRoleAssignmentUri,
                                resourceProviderNamespace).ConfigureAwait(false);
                        },
                        concurrencyLimit: 1)
                    .ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Gets the role assignments for a given principal.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="additionalHeaders">Optional. Additional headers.</param>
        public async Task<RoleAssignmentDefinition[]> GetRoleAssignments(
            string authenticationTenantId,
            string scope,
            string principalId,
            string roleDefinitionId,
            string resourceProviderNamespace,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            this.EventSource.Debug(
                operationName: "FrontdoorEngine.GetRoleAssignments",
                format: "Getting Role Assignments for Principal: {0} in resourceProviderNamespace '{1}' with Scope: {2} and authenticationTenantId '{3}'. RoleDefinitionId = {4}. additionalHeaders = {5}",
                arg0: principalId,
                arg1: resourceProviderNamespace,
                arg2: scope,
                arg3: authenticationTenantId,
                arg4: roleDefinitionId,
                arg5: string.Join(",", additionalHeaders?.Select(kvp => kvp.Key + ":" + kvp.Value)));

            var roleAssignmentsRequestUri = UriTemplateEngine.GetRoleAssignmentsForPrincipalRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                scope: scope,
                principalId: principalId,
                apiVersion: FrontdoorEngine.AuthorizationApiVersion);

            var roleAssignments = await this
                .GetAuthorizationResources<ResponseWithContinuation<RoleAssignmentDefinition[]>>(
                    authenticationTenantId: authenticationTenantId,
                    requestUri: roleAssignmentsRequestUri,
                    resourceProviderNamespace: resourceProviderNamespace,
                    additionalHeaders: additionalHeaders)
                .ConfigureAwait(continueOnCapturedContext: false);

            return roleAssignments.Value
                .CoalesceEnumerable()
                .Where(assignment => assignment.Properties.RoleDefinitionId.Trim('/').Split('/').Last().EqualsInsensitively(roleDefinitionId))
                .Where(assignment => NormalizationUtility.ScopeEquals(assignment.Properties.Scope, scope))
                .ToArray();
        }

        /// <summary>
        /// Registers a subscription with a managed tenant.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="publisherTenantId">The publisher tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="resourceId">The resource Id to map with managed by tenant registration.</param>
        public async Task RegisterSubscriptionWithManagedTenant(
            string authenticationTenantId,
            string subscriptionId,
            string publisherTenantId,
            string resourceProviderNamespace,
            string apiVersion = null,
            string resourceId = null)
        {
            apiVersion = apiVersion ?? ProviderConstants.Databricks.DefaultManagedByTenantApiVersion;
            var definition = this.GetManagedByTenantDefinition(apiVersion, resourceId);

            var registerSubscriptionUri = UriTemplateEngine.GetManagedTenantRegisterRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                tenantId: publisherTenantId,
                apiVersion: apiVersion);

            this.EventSource.Debug(
                operationName: "FrontdoorEngine.RegisterSubscriptionWithManagedTenant",
                format: "Registering Subscription '{0}' with Managed Tenant '{1}' in authenticationTenant: '{2}' with registerSubscriptionUri {3}",
                arg0: subscriptionId,
                arg1: publisherTenantId,
                arg2: authenticationTenantId,
                arg3: registerSubscriptionUri.ToString());

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService<ManagedByTenantDefinition>(
                    method: HttpMethod.Post,
                    requestUri: registerSubscriptionUri,
                    requestBody: definition,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.EventSource.Debug(
                        operationName: "FrontdoorEngine.RegisterSubscriptionWithManagedTenant",
                        format: "IsSuccessfulRequest: {0}. Error Code: {1}. Error Message: {2}",
                        arg0: response.StatusCode.IsSuccessfulRequest(),
                        arg1: errorResponseMessage?.Error?.Code,
                        arg2: errorResponseMessage?.Error?.Message);

                    if (errorResponseMessage != null)
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }
                }
            }
        }

        /// <summary>
        /// Unregisters a subscription with a managed tenant.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="publisherTenantId">The publisher tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="resourceId">The resource Id to map with managed by tenant registration.</param>
        public async Task UnregisterSubscriptionWithManagedTenant(
            string authenticationTenantId,
            string subscriptionId,
            string publisherTenantId,
            string resourceProviderNamespace,
            string apiVersion = null,
            string resourceId = null)
        {
            apiVersion = apiVersion ?? ProviderConstants.Databricks.DefaultManagedByTenantApiVersion;
            var definition = this.GetManagedByTenantDefinition(apiVersion, resourceId);

            var registerSubscriptionUri = UriTemplateEngine.GetManagedTenantUnregisterRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                tenantId: publisherTenantId,
                apiVersion: apiVersion);

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService<ManagedByTenantDefinition>(
                    method: HttpMethod.Post,
                    requestUri: registerSubscriptionUri,
                    requestBody: definition,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    if (errorResponseMessage != null)
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }
                }
            }
        }

        /// <summary>
        /// Get ManagedByTenantDefinition based on Resource ID
        /// </summary>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="resourceId">The resource Id to map with managed by tenant registration.</param>
        /// <returns>Returns ManagedByTenantDefinition </returns>
        private ManagedByTenantDefinition GetManagedByTenantDefinition(string apiVersion, string resourceId = null)
        {
            var definition = default(ManagedByTenantDefinition);

            if (apiVersion.IsGreaterThanOrEqualTo(ProviderConstants.ApiVersion20190301))
            {
                if (string.IsNullOrWhiteSpace(resourceId))
                {
                    string invalidTenantRequestMessage = ErrorResponseMessages.RegisterManagedByTenantResourceIdRequired
                        .ToLocalizedMessage(apiVersion);

                    throw new ErrorResponseMessageException(
                        httpStatus: HttpStatusCode.BadRequest,
                        errorCode: ErrorResponseCode.InvalidManagedByTenantRequest,
                        errorMessage: invalidTenantRequestMessage);
                }

                definition = new ManagedByTenantDefinition
                {
                    ResourceId = resourceId
                };
            }

            return definition;
        }

        /// <summary>
        /// Creates an authorization resource.
        /// </summary>
        /// <typeparam name="T">The type of authorization request body.</typeparam>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="authorizationResourceDefinition">The resource definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateAuthorizationResource<T>(
            string authenticationTenantId,
            Uri requestUri,
            T authorizationResourceDefinition,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(
                operationName,
                $"Creating Authorization Resource '{authorizationResourceDefinition.ToJson()}'" +
                $" in authenticationTenant: '{authenticationTenantId}' in resourceProviderNamespace" +
                $" '{resourceProviderNamespace}' with requestUri {requestUri}");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Put,
                    requestUri: requestUri,
                    requestBody: authorizationResourceDefinition,
                    accessToken: authenticationToken.AccessToken,
                    userAgent: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.EventSource.Debug(
                        operationName,
                        $"IsSuccessfulRequest: {response.StatusCode.IsSuccessfulRequest()}." +
                        $" Error Code: {errorResponseMessage?.Error?.Code}." +
                        $" Error Message: {errorResponseMessage?.Error?.Message}");

                    if (errorResponseMessage != null)
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                                RequestCorrelationContext.Current.CorrelationId));
                    }
                }
            }
        }

        /// <summary>
        /// Deletes an authorization resource.
        /// </summary>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task DeleteAuthorizationResource(
            string authenticationTenantId,
            Uri requestUri,
            string resourceProviderNamespace)
        {
            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Delete,
                    requestUri: requestUri,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest() && response.StatusCode != HttpStatusCode.NotFound)
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.EventSource.Debug(
                        this.GetOperationName(Utilities.GetAsyncMethodName()),
                        $"Failed to delete authorization resource {0}. " +
                        $"Error Code: {errorResponseMessage?.Error?.Code}." +
                        $"Error Message: {errorResponseMessage?.Error?.Message}");

                    if (errorResponseMessage?.Error != null)
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                                RequestCorrelationContext.Current.CorrelationId));
                    }
                }
            }
        }

        /// <summary>
        /// Creates an authorization resource.
        /// </summary>
        /// <typeparam name="T">The type of authorization request body.</typeparam>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="additionalHeaders">Optional. Additional headers.</param>
        public async Task<T> GetAuthorizationResources<T>(
            string authenticationTenantId,
            Uri requestUri,
            string resourceProviderNamespace,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Get,
                    requestUri: requestUri,
                    accessToken: authenticationToken.AccessToken,
                    additionalHeaders: additionalHeaders)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    if (errorResponseMessage != null)
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }
                }
                else
                {
                    return await response.Content
                        .ReadAsJsonAsync<T>(MediaTypeFormatter)
                        .ConfigureAwait(continueOnCapturedContext: false);
                }
            }
        }

        /// <summary>
        /// Get Databricks Account API endpoint and audience, based on feature flag
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        public async Task<(Uri, string, bool)> GetDatabricksAccountApiEnpointAndAudience(string tenantId, string subscriptionId)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName, $"START: Getting Databricks Account API endpoint and audience for tenantId: '{tenantId}', subscriptionId: '{subscriptionId}'");

            try
            {
                var registeredFeatures = await this.GetRegisteredFeaturesInSubscription(
                        tenantId,
                        subscriptionId,
                        ProviderConstants.Databricks.ResourceProviderNamespace)
                    .ConfigureAwait(false);

                (Uri baseUri, string aadAudience, bool useArm) = DatabricksAccountsManagerUtils.GetBaseUriAndAadAudience(registeredFeatures, this.EventSource);
                if (useArm)
                {
                    aadAudience = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience");
                }
                this.EventSource.Debug(operationName, $"SUCCESS: Got Databricks Account API endpoint: '{baseUri}' and audience: '{aadAudience}'");
                return (baseUri, aadAudience, useArm);
            }
            catch (Exception ex)
            {
                this.EventSource.Error(operationName, $"ERROR: Failed to get Databricks Account API endpoint and audience. Error: {Utilities.FlattenException(ex)}");
                throw;
            }
        }

        /// <summary>
        /// Gets the system deny assignments for a given principal.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<DenyAssignmentDefinition[]> GetSystemDenyAssignments(
            string authenticationTenantId,
            string scope,
            string principalId,
            string resourceProviderNamespace)
        {
            var denyAssignmentsRequestUri = UriTemplateEngine.GetDenyAssignmentsForPrincipalRequestUri(
                this.FrontdoorEndpointUri,
                scope,
                principalId,
                DenyAssignmentsApiVersion);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Getting deny assignments for {denyAssignmentsRequestUri.AbsoluteUri} ");

            var denyAssignments = await this
                .GetAuthorizationResources<ResponseWithContinuation<DenyAssignmentDefinition[]>>(
                    authenticationTenantId,
                    denyAssignmentsRequestUri,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Got {denyAssignments.Value.CoalesceEnumerable().Length} deny assignments for scope '{scope}', principalId: {principalId}");

            // Filters the system deny assignments
            return denyAssignments.Value
                .CoalesceEnumerable()
                .Where(denyAssignment => denyAssignment.Properties.GetProperty(ProviderConstants.Authorizations.IsSystemProtectedProperty).ToObject<bool>()
                                         && NormalizationUtility.NormalizeScope(denyAssignment.Properties.GetProperty(ProviderConstants.Authorizations.ScopeProperty).ToObject<string>()).EqualsInsensitively(NormalizationUtility.NormalizeScope(scope)))
                .ToArray();
        }

        /// <summary>
        /// Creates a deny assignment if it does not exist or updated a deny assignment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope on which deny assignment is to be created.</param>
        /// <param name="principalId">The principal Id.</param>
        /// <param name="denyAssignmentDefinition">The deny assignment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateOrUpdateDenyAssignments(
            string authenticationTenantId,
            string scope,
            string principalId,
            DenyAssignmentDefinition denyAssignmentDefinition,
            string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var systemDenyAssignments = await this
                .GetSystemDenyAssignments(
                    authenticationTenantId,
                    scope,
                    principalId,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            string denyAssignmentResourceId = null;
            if (!systemDenyAssignments.CoalesceEnumerable().Any())
            {
                denyAssignmentResourceId = Guid.NewGuid().ToString();

                this.EventSource.Debug(operationName,
                    $"Creating a new system deny assignment '{denyAssignmentResourceId}' for the scope '{scope}' with requestBody: {denyAssignmentDefinition}");
            }
            else if (systemDenyAssignments.Length == 1)
            {
                denyAssignmentResourceId = this.GetSystemDenyAssignmentResourceId(systemDenyAssignments.First(), scope);

                this.EventSource.Debug(operationName,
                    $"Updating existing system deny assignment '{denyAssignmentResourceId}' for the scope '{scope}' with requestBody: {denyAssignmentDefinition}");
            }
            else
            {
                this.EventSource.Error(
                    operationName,
                    $"Request to create or update the system deny assignment with the scope {scope} failed." +
                    $" Details: unexpected system deny assignments count {systemDenyAssignments.Length}");

                throw new AuthorizationOperationException(
                    HttpStatusCode.InternalServerError,
                    ErrorResponseCode.InternalServerError.ToString(),
                    ErrorResponseMessages.DenyAssignmentCreateOrUpdateError.ToLocalizedMessage(scope));
            }

            await this
                .CreateOrUpdateDenyAssignmentsWithResourceId(
                    authenticationTenantId,
                    scope,
                    denyAssignmentResourceId,
                    denyAssignmentDefinition,
                    resourceProviderNamespace)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Creates a deny assignment if it does not exist or updated a deny assignment with the resource id.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="denyAssignmentResourceId">The deny assignment resource id.</param>
        /// <param name="denyAssignmentDefinition">The deny assignment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateOrUpdateDenyAssignmentsWithResourceId(
            string authenticationTenantId,
            string scope,
            string denyAssignmentResourceId,
            DenyAssignmentDefinition denyAssignmentDefinition,
            string resourceProviderNamespace)
        {
            var denyAssignmentRequestUri = UriTemplateEngine.GetDenyAssignmentRequestUri(
                this.FrontdoorEndpointUri,
                scope,
                denyAssignmentResourceId,
                FrontdoorEngine.DenyAssignmentsApiVersion);

            await this
                .CreateAuthorizationResource(
                    authenticationTenantId,
                    denyAssignmentRequestUri,
                    denyAssignmentDefinition,
                    resourceProviderNamespace)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Gets the system deny assignment resource id.
        /// </summary>
        /// <param name="systemDenyAssignmentDefinition">The system deny assignment definition.</param>
        /// <param name="scope">The scope.</param>
        public string GetSystemDenyAssignmentResourceId(DenyAssignmentDefinition systemDenyAssignmentDefinition, string scope)
        {
            var match = UriTemplateEngine.DenyAssignmentResourceIdTemplate.GetTemplateMatch(systemDenyAssignmentDefinition.Id);

            if (match != null)
            {
                return match.BoundVariables["resourceId"];
            }

            this.EventSource.Error(
                "FrontdoorEngine.GetSystemDenyAssignmentResourceId",
                $"Request to get the system deny assignment with the scope {scope} failed. Details: no valid deny assignment resource id");

            throw new AuthorizationOperationException(
                HttpStatusCode.InternalServerError,
                ErrorResponseCode.InternalServerError.ToString(),
                ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
        }

        /// <summary>
        /// Delete a system deny assignment.
        /// </summary>
        /// <param name="authenticationTenantId">The customer home tenant Id used to retrieve access token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="principalId">The principal id.In this case EveryonePrincipalId</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        public async Task DeleteSystemDenyAssignment(
            string authenticationTenantId,
            string scope,
            string principalId,
            string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var systemDenyAssignments = await this
                .GetSystemDenyAssignments(
                    authenticationTenantId,
                    scope,
                    principalId,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            if (systemDenyAssignments == null || !systemDenyAssignments.CoalesceEnumerable().Any())
            {
                this.EventSource.Debug(operationName, $"Deny Assignment with tenantId {authenticationTenantId} not found. Skipping deletion for the scope '{scope}'");
                return;
            }

            string denyAssignmentResourceId = this.GetSystemDenyAssignmentResourceId(systemDenyAssignments.First(), scope);

            var denyAssignmentRequestUri = UriTemplateEngine.GetDenyAssignmentRequestUri(
                this.FrontdoorEndpointUri,
                scope,
                denyAssignmentResourceId,
                DenyAssignmentsApiVersion);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Deleting deny assignments for {denyAssignmentRequestUri.AbsoluteUri} ");

            await this.DeleteAuthorizationResource(authenticationTenantId, denyAssignmentRequestUri, resourceProviderNamespace);
        }

        #endregion

        #region Deployments

        /// <summary>
        /// Creates a template deployment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="deploymentName">The deployment name.</param>
        /// <param name="deploymentDefinition">The deployment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="forceTokenRefresh">Optional. Indicates whether to force refresh the token.</param>
        public async Task<DeploymentResponse> CreateDeployment(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string deploymentName,
            DeploymentDefinition deploymentDefinition,
            string resourceProviderNamespace,
            bool forceTokenRefresh = false)
        {
            var deploymentUri = UriTemplateEngine.GetDeploymentRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                deploymentName: deploymentName,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion);

            // Always use ARM audience for deployment operations
            string armAudience = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId,
                    audience: armAudience,
                    forceRefresh: forceTokenRefresh)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Put,
                    requestUri: deploymentUri,
                    requestBody: deploymentDefinition,
                    accessToken: authenticationToken.AccessToken,
                    userAgent: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (response.StatusCode.IsSuccessfulRequest())
                {
                    var outputDeploymentDefinition = await response.Content
                        .ReadAsJsonAsync<DeploymentDefinition>(MediaTypeFormatter)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    return new DeploymentResponse
                    {
                        OutputDefinition = outputDeploymentDefinition,
                        AzureAsyncOperationUri = new Uri(response.Headers.GetValues(RequestCorrelationContext.HeaderAzureAsyncOperation).Single())
                    };
                }

                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (errorResponseMessage != null)
                {
                    throw new DeploymentOperationException(
                        httpStatus: response.StatusCode,
                        errorCode: errorResponseMessage.Error.Code,
                        errorMessage: errorResponseMessage.Error.Message);
                }
                else
                {
                    throw new DeploymentOperationException(
                        httpStatus: HttpStatusCode.InternalServerError,
                        errorCode: ErrorResponseCode.InternalServerError.ToString(),
                        errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                }
            }
        }

        /// <summary>
        /// Gets a template deployment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="deploymentName">The deployment name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<DeploymentDefinition> GetDeployment(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string deploymentName,
            string resourceProviderNamespace)
        {
            var deploymentUri = UriTemplateEngine.GetDeploymentRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                resourceGroupName: resourceGroupName,
                deploymentName: deploymentName,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion);

            // Always use ARM audience for deployment operations
            string armAudience = CloudConfigurationManager.GetConfiguration("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.AadAllowedAudience");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId,
                    audience: armAudience)
                .ConfigureAwait(continueOnCapturedContext: false);

            var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Get,
                    requestUri: deploymentUri,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (response.StatusCode.IsSuccessfulRequest())
            {
                return await response.Content
                    .ReadAsJsonAsync<DeploymentDefinition>(MediaTypeFormatter)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
            else if (response.StatusCode == HttpStatusCode.NotFound)
            {
                return null;
            }

            var errorResponseMessage = await this
                .GetErrorResponseMessage(response)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (errorResponseMessage != null)
            {
                throw new DeploymentOperationException(
                    httpStatus: response.StatusCode,
                    errorCode: errorResponseMessage.Error.Code,
                    errorMessage: errorResponseMessage.Error.Message);
            }
            else
            {
                throw new DeploymentOperationException(
                    httpStatus: HttpStatusCode.InternalServerError,
                    errorCode: ErrorResponseCode.InternalServerError.ToString(),
                    errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
            }
        }

        /// <summary>
        /// Gets status of a deployment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="forceTokenRefresh">Optional. Indicates whether to force refresh the token.</param>
        public async Task<AsyncOperationResult> GetDeploymentOperationStatus(
            string authenticationTenantId,
            Uri operationTrackingUri,
            string resourceProviderNamespace,
            bool forceTokenRefresh = false)
        {
            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId,
                    forceRefresh: forceTokenRefresh)
                .ConfigureAwait(continueOnCapturedContext: false);

            var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Get,
                    requestUri: operationTrackingUri,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (response.StatusCode.IsSuccessfulRequest())
            {
                return await response.Content
                    .ReadAsJsonAsync<AsyncOperationResult>(MediaTypeFormatter)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
            else
            {
                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (errorResponseMessage != null)
                {
                    throw new DeploymentOperationException(
                        httpStatus: response.StatusCode,
                        errorCode: errorResponseMessage.Error.Code,
                        errorMessage: errorResponseMessage.Error.Message);
                }
                else
                {
                    throw new DeploymentOperationException(
                        httpStatus: HttpStatusCode.InternalServerError,
                        errorCode: ErrorResponseCode.InternalServerError.ToString(),
                        errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                }
            }
        }

        /// <summary>
        /// Gets status of a resource group deletion.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<AsyncOperationResult> GetResourceGroupDeletionStatus(
            string authenticationTenantId,
            Uri operationTrackingUri,
            string resourceProviderNamespace)
        {
            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Get,
                    requestUri: operationTrackingUri,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false);

            if (response.StatusCode == HttpStatusCode.Accepted)
            {
                return new AsyncOperationResult
                {
                    Status = ProvisioningState.Accepted.ToString()
                };
            }
            else if (response.StatusCode.IsSuccessfulRequest() || response.StatusCode == HttpStatusCode.NotFound)
            {
                return new AsyncOperationResult
                {
                    Status = ProvisioningState.Succeeded.ToString()
                };
            }
            else
            {
                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(continueOnCapturedContext: false);

                if (errorResponseMessage != null)
                {
                    throw new ResourceGroupOperationException(
                        httpStatus: response.StatusCode,
                        errorCode: errorResponseMessage.Error.Code,
                        errorMessage: errorResponseMessage.Error.Message + errorResponseMessage.Error.Details?.Select(ex => ex.Message).ToArray().ConcatStrings());
                }
                else
                {
                    throw new ResourceGroupOperationException(
                        httpStatus: HttpStatusCode.InternalServerError,
                        errorCode: ErrorResponseCode.InternalServerError.ToString(),
                        errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                }
            }
        }

        /// <summary>
        /// Gets status of a bulk delete operation.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<BulkDeleteOperationResult> GetBulkDeleteOperationStatus(
            string authenticationTenantId,
            Uri operationTrackingUri,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Get,
                    requestUri: operationTrackingUri,
                    accessToken: authenticationToken.AccessToken)
                .ConfigureAwait(continueOnCapturedContext: false);

            //ARM will always return response and response code won't be NotFound anytime,
            //if it happens some issue with ARM, this needs to be investigated with ARM.
            if (response != null && response.StatusCode != HttpStatusCode.NotFound)
            {
                this.EventSource.Debug(operationName, $"Bulk delete opertion status responseCode: {response.StatusCode}," +
                    $" responseBody: '{await response.Content.ReadAsStringAsync().ConfigureAwait(false)}'");

                return await response.Content
                    .ReadAsJsonAsync<BulkDeleteOperationResult>(MediaTypeFormatter)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
            else
            {
                this.EventSource.Debug(operationName, $"Unexpected response recevied from ARM," +
                    $" responseCode: '{response?.StatusCode}'");

                throw new BulkDeleteOperationException(
                        httpStatus: HttpStatusCode.InternalServerError,
                        errorCode: ErrorResponseCode.InternalServerError.ToString(),
                        errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                            RequestCorrelationContext.Current.CorrelationId));
            }
        }

        #endregion

        #region Resource Providers

        /// <summary>
        /// Registers subscription for a resource provider.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="authenticationResourceProviderNamespace">The authentication resource provider namespace used to get the token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceProvidersToRegister">The resource provider namespace to register the subscription for.</param>
        /// <param name="signedOboToken">Signed Obo Token</param>
        public async Task RegisterSubscriptionForResourceProviders(
            string authenticationTenantId,
            string authenticationResourceProviderNamespace,
            string subscriptionId,
            List<string> resourceProvidersToRegister,
            string signedOboToken)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            ProvidersLog.Current.Debug(
                operationName,
                 message: $"OboToken is present in the header: '{!string.IsNullOrWhiteSpace(signedOboToken)}'.");

            if (string.IsNullOrWhiteSpace(signedOboToken))
            {
                return;
            }

            var authenticationToken = await this
                 .GetAuthenticationTokenFromCache(
                     tenantId: authenticationTenantId)
                 .ConfigureAwait(continueOnCapturedContext: false);

            HashSet<string> alreadyRegisteredProviders = (await GetRegisteredResourceProvidersForSubscription(subscriptionId, authenticationTenantId))
                                                          .Select(a => a.Namespace).ToHashSet<string>();

            foreach (var resourceProviderToRegister in resourceProvidersToRegister)
            {

                if (!alreadyRegisteredProviders.Contains(resourceProviderToRegister, StringComparer.OrdinalIgnoreCase))
                {
                    this.EventSource.Debug(
                    operationName: operationName,
                    format: "Registering Subscription '{0}' for Resource Provider, ResourceProviderToRegister '{1}', AuthenticationTenantId '{2}', AuthenticationResourceProviderNamespace '{3}'",
                    arg0: subscriptionId,
                    arg1: resourceProviderToRegister,
                    arg2: authenticationTenantId,
                    arg3: authenticationResourceProviderNamespace);

                    var oboTokenHeaders = Utilities.GetOboTokenHeaders(signedOboToken);

                    var registerProviderUri = UriTemplateEngine.GetSubscriptionResourceProviderRegistrationUri(
                         endpoint: this.FrontdoorEndpointUri,
                         subscriptionId: subscriptionId,
                         resourceProviderNamespace: resourceProviderToRegister,
                         apiVersion: FrontdoorEngine.FrontdoorApiVersion);

                    using (var response = await this.FrontdoorClient
                         .CallFrontdoorService(
                             method: HttpMethod.Post,
                             requestUri: registerProviderUri,
                             accessToken: authenticationToken.AccessToken,
                             additionalHeaders: oboTokenHeaders.ToArray())
                         .ConfigureAwait(continueOnCapturedContext: false))
                    {
                        if (!response.StatusCode.IsSuccessfulRequest())
                        {
                            var errorResponseMessage = await this
                                .GetErrorResponseMessage(response)
                                .ConfigureAwait(continueOnCapturedContext: false);

                            var errorMessage = string.Format(
                                 "Registering the subscription '{0}' to resource provider '{1}' failed with error '{2}'",
                                 subscriptionId,
                                 resourceProviderToRegister,
                                 errorResponseMessage != null ? errorResponseMessage.Error.Message : string.Empty);

                            this.EventSource.Error(
                                 operationName: operationName,
                                 message: errorMessage);

                            if (errorResponseMessage != null)
                            {
                                throw new AuthorizationOperationException(
                                    httpStatus: response.StatusCode,
                                    errorCode: errorResponseMessage.Error.Code,
                                    errorMessage: errorResponseMessage.Error.Message);
                            }
                            else
                            {
                                throw new AuthorizationOperationException(
                                    httpStatus: HttpStatusCode.InternalServerError,
                                    errorCode: ErrorResponseCode.InternalServerError.ToString(),
                                    errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                            }
                        }
                        else
                        {
                            ProvidersLog.Current.Debug(
                                operationName,
                                message: $"Subscription '{subscriptionId}' is registered for resource provider '{resourceProviderToRegister}'");
                        }
                    }
                }
                else
                {
                    ProvidersLog.Current.Debug(
                                operationName,
                                message: $"Subscription '{subscriptionId}' is already registered for the required Resource Paroviders'");
                }
            }
        }

        /// <summary>
        /// Fetches all the registered resource provider for the subscription
        /// </summary>
        /// <param name="subscriptionId">Subscription Id</param>
        /// <param name="authenticationTenantId">Tenant Id of the Subscription</param>
        /// <returns>Returns a task that, when awaited, will produce a collection of ResourceProvider objects.</returns>
        /// <exception cref="AuthorizationOperationException"></exception>
        /// <exception cref="ServerErrorResponseMessageException"></exception>
        public async Task<IEnumerable<ResourceProvider>> GetRegisteredResourceProvidersForSubscription(
            string subscriptionId,
            string authenticationTenantId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName,
                $"Attempt to get the token from cache for tenantid {authenticationTenantId} and subscriptionid {subscriptionId}");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            var listResourceProvidersUri = UriTemplateEngine.GetSubscriptionResourceProvidersRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion);

            using (var response = await AsyncRetry.Retry(
                           () => this.FrontdoorClient
                               .CallFrontdoorService(
                                   method: HttpMethod.Get,
                                   requestUri: listResourceProvidersUri,
                                   accessToken: authenticationToken.AccessToken),
                           ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                           TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                           isRetryable: ex =>
                               ex.IsConnectivityException() ||
                               (ex is ServerErrorResponseMessageException exception &&
                                exception.HttpStatus.IsRetryableResponse()),
                           errorAction: (Exception exception) =>
                           {
                               this.EventSource.Debug(operationName,
                                   $"Attempt to GET '{listResourceProvidersUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                           })
                       .ConfigureAwait(false))
            {

                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    string error = errorResponseMessage != null ? errorResponseMessage.Error.Message : string.Empty;

                    var errorMessage = string.Format(
                        $"Unable to get registered resource provider for subscription '{subscriptionId}' failed with error '{error}'");

                    this.EventSource.Error(
                        operationName: operationName,
                        message: errorMessage);

                    if (errorResponseMessage != null &&
                        (response.StatusCode == HttpStatusCode.Unauthorized ||
                         response.StatusCode == HttpStatusCode.Forbidden))
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new ServerErrorResponseMessageException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                                RequestCorrelationContext.Current.CorrelationId));
                    }
                }

                var responseContent = await response.Content.ReadAsStringAsync()
                    .ConfigureAwait(false);

                ProvidersLog.Current.Debug(
                    operationName,
                    message: $"ARM Resource provider returned response {responseContent}.");

                JObject responseJson = JObject.Parse(responseContent);

                JToken responseValue = responseJson["value"];

                var registeredResourceProvider = responseValue.ToObject<IEnumerable<ResourceProvider>>().Where(x => x.RegistrationState != null &&
                                                                (x.RegistrationState == SubscriptionRegistrationState.Registered ||
                                                                x.RegistrationState == SubscriptionRegistrationState.Registering));

                ProvidersLog.Current.Debug(operationName,
                        message: $"Subscription is registered for resource providers: {JsonConvert.SerializeObject(registeredResourceProvider)}");

                return registeredResourceProvider;
            }
        }

        /// <summary>
        /// Is Resource Provider Registered
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="registrationResourceProviderNamespace">The authentication resource provider namespace used to get the token.</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <returns></returns>
        public async Task<bool> IsResourceProviderRegistered(
            string subscriptionId,
            string registrationResourceProviderNamespace,
            string authenticationTenantId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            var isRegistered = false;
            var registerProviderUri = UriTemplateEngine.GetSubscriptionResourceProviderRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                subscriptionId: subscriptionId,
                resourceProviderNamespace: registrationResourceProviderNamespace,
                apiVersion: FrontdoorEngine.FrontdoorApiVersion);

            using (var response = await AsyncRetry.Retry(
                           () => this.FrontdoorClient
                               .CallFrontdoorService(
                                   method: HttpMethod.Get,
                                   requestUri: registerProviderUri,
                                   accessToken: authenticationToken.AccessToken),
                           ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                           TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                           isRetryable: ex =>
                               ex.IsConnectivityException() ||
                               (ex is ServerErrorResponseMessageException exception &&
                                exception.HttpStatus.IsRetryableResponse()),
                           errorAction: (Exception exception) =>
                           {
                               this.EventSource.Debug(operationName,
                                   $"Attempt to GET '{registerProviderUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                           })
                       .ConfigureAwait(false))
            {

                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    var errorMessage = string.Format(
                        "Unable to get resource provider '{0}' registration for subscription '{1}' failed with error '{2}'",
                        registrationResourceProviderNamespace,
                        subscriptionId,
                        errorResponseMessage != null
                            ? errorResponseMessage.Error.Message
                            : string.Empty);

                    this.EventSource.Error(
                        operationName: "FrontdoorEngine.IsResourceProviderRegistered",
                        message: errorMessage);

                    if (errorResponseMessage != null &&
                        (response.StatusCode == HttpStatusCode.Unauthorized ||
                         response.StatusCode == HttpStatusCode.Forbidden))
                    {
                        throw new AuthorizationOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new ServerErrorResponseMessageException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(
                                RequestCorrelationContext.Current.CorrelationId));
                    }
                }

                var responseContent = await response.Content.ReadAsStringAsync()
                    .ConfigureAwait(false);

                ProvidersLog.Current.Debug(
                    operationName,
                    message: $"ARM Resource provider returned response {responseContent}.");

                var result = JsonConvert.DeserializeObject<ResourceProvider>(responseContent);

                var registrationState = result.RegistrationState;

                if (registrationState != null &&
                    (registrationState == SubscriptionRegistrationState.Registered ||
                     registrationState == SubscriptionRegistrationState.Registering))
                {
                    ProvidersLog.Current.Debug(
                        operationName,
                        message: $"Resource Provider {registrationResourceProviderNamespace} already registered with subscription {subscriptionId}");

                    isRegistered = true;
                }

                return isRegistered;
            }
        }

        /// <summary>
        /// Calls front door.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="requestUri">The request uri.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="requestBody">The request body.</param>
        public async Task<HttpResponseMessage> CallFrontdoor(
            HttpRequestMessage httpRequest,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            JToken requestBody)
        {
            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: tenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            try
            {
                using (var response = requestBody != null
                    ? await this.FrontdoorClient
                        .CallFrontdoorService(
                            method: httpRequest.Method,
                            requestUri: requestUri,
                            accessToken: authenticationToken.AccessToken,
                            requestBody: requestBody)
                        .ConfigureAwait(continueOnCapturedContext: false)
                    : await this.FrontdoorClient
                        .CallFrontdoorService(
                            method: httpRequest.Method,
                            requestUri: requestUri,
                            accessToken: authenticationToken.AccessToken)
                        .ConfigureAwait(continueOnCapturedContext: false))
                {
                    // TODO(olenas): WI #2760412 - Ensure same response is returned
                    var httpResponse = httpRequest.CreateResponse(response.StatusCode);

                    if (response.StatusCode == HttpStatusCode.Accepted)
                    {
                        httpResponse.Headers.Location = response.Headers.Location;
                        httpResponse.Headers.RetryAfter = response.Headers.RetryAfter;
                    }

                    var responseContent = response.Content != null
                        ? await response.Content.ReadAsStringAsync().ConfigureAwait(continueOnCapturedContext: false)
                        : null;

                    if (responseContent != null)
                    {
                        httpResponse.Content = new StringContent(responseContent, Encoding.UTF8, "application/json");
                    }

                    if (!httpResponse.Headers.Contains(RequestCorrelationContext.HeaderServiceRequestId))
                    {
                        httpResponse.Headers.Add(RequestCorrelationContext.HeaderServiceRequestId, RequestCorrelationContext.Current.CurrentActivityId);
                    }

                    return httpResponse;
                }
            }
            catch (HttpRequestException)
            {
                this.EventSource.Error(
                    operationName: "FrontdoorEngine.CallFrontdoor",
                    format: "Call '{0}' to uri '{1}' failed due to failed connection",
                    arg0: httpRequest.Method,
                    arg1: requestUri);

                throw new ErrorResponseMessageException(
                    httpStatus: HttpStatusCode.InternalServerError,
                    errorCode: ErrorResponseCode.CallToCustomProviderFailed,
                    errorMessage: ErrorResponseMessages.CallToCustomProviderFailedWithException.ToLocalizedMessage(requestUri));
            }
        }

        #endregion

        #region Authentication

        /// <summary>
        /// Gets the authentication token.
        /// </summary>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="forceRefresh">Optional. Indicates whether to force refresh the token.</param>
        public Task<AuthenticationResult> GetAuthenticationTokenFromCache(string tenantId, string audience = null, bool forceRefresh = false)
        {
            this.EventSource.Debug(
                operationName: "FrontdoorEngine.GetAuthenticationTokenFromCache",
                format: "Getting cached AAD token with tenantId '{0}'",
                arg0: tenantId);

            return this.CacheProvidersContainer
                .GetAuthenticationTokenCacheProvider()
                .GetAuthenticationToken(tenantId: tenantId, audience: audience, forceRefresh: forceRefresh);
        }

        /// <summary>
        /// Gets the authentication token.
        /// </summary>
        /// <param name="authenticationUri">Authentication authority Uri</param>
        /// <param name="tenantId">Tenant Id to be authenticated against</param>
        /// <param name="resource">Audience for AAD Token</param>
        /// <returns>Task for authentication token</returns>
        public Task<AuthenticationResult> GetAuthenticationTokenFromAad(Uri authenticationUri, string tenantId, string resource)
        {
            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Getting AAD token with tenantId: '{tenantId}', Authority: '{authenticationUri.Authority}', target resource: '{resource}'");

            return this.CacheProvidersContainer
                .GetAuthenticationTokenCacheProvider()
                .GetAuthenticationTokenFromAad(tenantId, resource, authenticationUri);
        }

        /// <summary>
        /// Gets the authentication token for given audience/target azure resource
        /// </summary>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="resource">Audience for AAD Token</param>
        /// <returns></returns>
        private Task<AuthenticationResult> GetAuthenticationTokenFromAad(string tenantId, string resource)
        {
            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Getting AAD token with tenantId: '{tenantId}', target resource: '{resource}'");

            return this.CacheProvidersContainer
                .GetAuthenticationTokenCacheProvider()
                .GetAuthenticationTokenFromAad(tenantId, resource);
        }

        /// <summary>
        /// Refreshes the authentication token in the cache.
        /// </summary>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public Task RefreshAuthenticationToken(string tenantId, string resourceProviderNamespace)
        {
            this.EventSource.Debug(
                operationName: "FrontdoorEngine.RefreshAuthenticationToken",
                format: "Refreshing AAD token with tenantId '{0}', resourceProviderNamespace '{1}'",
                arg0: tenantId,
                arg1: resourceProviderNamespace);

            return this.CacheProvidersContainer
                .GetAuthenticationTokenCacheProvider()
                .RefreshAuthenticationToken(tenantId);
        }

        /// <summary>
        /// Fetches the latest ARM metadata by querying the ARM endpoint.
        /// </summary>
        /// <returns>The ARM metadata object containing the certificates used by ARM</returns>
        public async Task<ArmMetadata> FetchLatestMetadata()
        {
            ProvidersLog.Current.Debug(
                operationName: "FrontdoorEngine.FetchLatestMetadata",
                message: $"Fetching latest thumbprints from ARM metadata provider endpoint {this.ArmMetadataEndpoint}.");

            var armMetadata = await AsyncRetry.Retry(
                async () =>
                {
                    var frontdoorClient = new FrontdoorClient();

                    using (var response = await frontdoorClient.CallFrontdoorService(
                        method: HttpMethod.Get,
                        requestUri: this.ArmMetadataEndpoint).ConfigureAwait(false))
                    {
                        ProvidersLog.Current.Debug(
                            operationName: "FrontdoorEngine.FetchLatestMetadata",
                            message: $"ARM metadata provider returned response status code {response.StatusCode.ToString()}.");

                        if (response.StatusCode.IsSuccessfulRequest())
                        {
                            var responseContent = await response.Content.ReadAsStringAsync().ConfigureAwait(false);

                            ProvidersLog.Current.Debug(
                                operationName: "FrontdoorEngine.FetchLatestMetadata",
                                message: $"ARM metadata provider returned response {responseContent}.");

                            return JsonConvert.DeserializeObject<ArmMetadata>(responseContent);
                        }
                        else
                        {
                            if (response.StatusCode.IsServerFailureRequest())
                            {
                                throw new MetadataEndpointRetryableException($"ARM Metadata endpoint failed with status code {((int)response.StatusCode).ToString()}");
                            }
                            else
                            {
                                throw new Exception($"ARM Metadata endpoint failed with status code {((int)response.StatusCode).ToString()}");
                            }
                        }
                    }
                },
                ArmMetadataRetryCount,
                TimeSpan.FromSeconds(ArmMetadataRetryInterval),
                isRetryable: ex => ex is MetadataEndpointRetryableException,
                errorAction: (Exception ex) =>
                {
                    ProvidersLog.Current.Error(
                        operationName: "FrontdoorEngine.FetchLatestMetadata",
                        message: "Querying the ARM metadata provider resulted in an exception",
                        exception: ex);
                })
                .ConfigureAwait(false);

            ProvidersLog.Current.Debug(
                operationName: "FrontdoorEngine.FetchLatestMetadata",
                 message: "ARM metadata provider queried successfully and the latest thumbprints have been cached.");

            return armMetadata;
        }



        #endregion

        #region Networking

        /// <summary>
        /// Prepare or un-prepare subnet with network intent policy.
        /// </summary>
        /// <param name="virtualNetworkInjectionOperation">The network intent policy operation on subnet</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subnetName">The subnet name.</param>
        /// <param name="requestUri">The request <c>uri</c>.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="virtualNetworkInjectionOperationRequest">The Virtual Network Injection Operation Request.</param>
        public async Task<HttpResponseHeaders> InitializeVirtualNetworkInjectionOperation(
            VirtualNetworkInjectionOperation virtualNetworkInjectionOperation,
            string authenticationTenantId,
            string subnetName,
            Uri requestUri,
            string resourceProviderNamespace,
            VirtualNetworkInjectionOperationRequest virtualNetworkInjectionOperationRequest)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    authenticationTenantId)
                .ConfigureAwait(false);

            bool isPrepare = virtualNetworkInjectionOperation == VirtualNetworkInjectionOperation.PrepareNetworkPolicies;
            string actionName = virtualNetworkInjectionOperation.ToString();

            using (var response = await AsyncRetry.Retry(
                    () => this.FrontdoorClient.CallFrontdoorService(
                                    HttpMethod.Post,
                                    requestUri,
                                    authenticationToken.AccessToken,
                                    isPrepare ? (PrepareNetworkPoliciesOperationRequest)virtualNetworkInjectionOperationRequest : virtualNetworkInjectionOperationRequest),
                    ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                    TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                    isRetryable: ex => ex.IsConnectivityException()
                                       || (ex is ServerErrorResponseMessageException
                                           && (((ServerErrorResponseMessageException)ex).HttpStatus.IsRetryableResponse()
                                                || ProviderConstants.MicrosoftNetwork.RetryableErrorCodes.ContainsInsensitively(((ServerErrorResponseMessageException)ex).ErrorCode.ToString()))),
                    errorAction: (Exception exception) =>
                    {
                        this.EventSource.Debug(operationName, $"Attempt to '{virtualNetworkInjectionOperation}' the subnet '{requestUri.AbsoluteUri}' failed. Framework will retry in {FrontdoorEngine.PreparePollingIntervalSeconds} seconds.Exception details: '{Utilities.FlattenException(exception)}'.");
                    })
                        .ConfigureAwait(false))
            {
                this.EventSource.Debug(operationName, $"{actionName} of the subnet '{subnetName}' is '{response?.StatusCode}'");

                if (response?.StatusCode == HttpStatusCode.Accepted)
                {
                    return response.Headers;
                }

                if (response == null || response.StatusCode.IsSuccessfulRequest())
                {
                    return null;
                }

                await this.HandleVirtualNetworkInjectionOperationErrorResponse(virtualNetworkInjectionOperation, response, subnetName, HttpMethod.Post);
            }

            return null;
        }

        /// <summary>
        /// Gets status of a prepare or un-prepare.
        /// </summary>
        /// <param name="virtualNetworkInjectionOperation">The virtual network injection operation on subnet</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subnetName">The subnet name.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<AsyncOperationResult> GetVirtualNetworkInjectionOperationStatus(
            VirtualNetworkInjectionOperation virtualNetworkInjectionOperation,
            string authenticationTenantId,
            string subnetName,
            Uri operationTrackingUri,
            string resourceProviderNamespace)
        {
            AsyncOperationResult asyncOperationResult = null;

            string actionName = virtualNetworkInjectionOperation.ToString();
            string methodName = Utilities.GetAsyncMethodName();

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    authenticationTenantId,
                    forceRefresh: true)
                .ConfigureAwait(false);

            // Prepare operation result should be ideally available in less than 5 minutes. Exit the loop if it is more.
            DateTime start = DateTime.UtcNow;
            while (DateTime.UtcNow.Subtract(start).TotalMinutes < 5)
            {
                HttpResponseMessage response = await AsyncRetry.Retry(
                        () => this.FrontdoorClient.CallFrontdoorService(HttpMethod.Get, operationTrackingUri, authenticationToken.AccessToken),
                        3,
                        TimeSpan.FromSeconds(PreparePollingIntervalSeconds))
                    .ConfigureAwait(false);

                this.EventSource.Debug(
                    this.GetOperationName(methodName),
                    "Polling {0} operation status for the subnet '{1}' - Status code: '{2}', Response: {3}",
                    actionName,
                    subnetName,
                    response?.StatusCode,
                    response?.Content?.ReadAsStringAsync().Result);

                if (response != null && response.StatusCode.IsSuccessfulRequest() && response.StatusCode != HttpStatusCode.Accepted)
                {
                    asyncOperationResult = await response.Content
                        .ReadAsJsonAsync<AsyncOperationResult>(MediaTypeFormatter)
                        .ConfigureAwait(false);

                    // If the operation status is succeeded or null then, return succeeded status
                    if (asyncOperationResult != null && asyncOperationResult.Status.EqualsInsensitively(ProvisioningState.Succeeded.ToString()))
                    {
                        break;
                    }

                    if (response.StatusCode == HttpStatusCode.OK && asyncOperationResult == null)
                    {
                        asyncOperationResult = new AsyncOperationResult { Status = ProvisioningState.Succeeded.ToString() };
                        break;
                    }
                }

                bool isAsyncOperationFailed = asyncOperationResult != null
                                              && (asyncOperationResult.Status.EqualsInsensitively(ProvisioningState.Failed.ToString())
                                                  || asyncOperationResult.Status.EqualsInsensitively(ProvisioningState.Canceled.ToString()));

                if ((response != null && !response.StatusCode.IsSuccessfulRequest()) || isAsyncOperationFailed)
                {
                    // Throw exception when the response is not successful or the async operation status is either failed or canceled.
                    await this.HandleVirtualNetworkInjectionOperationErrorResponse(virtualNetworkInjectionOperation, response, subnetName, HttpMethod.Get).ConfigureAwait(false);
                }

                this.EventSource.Debug(
                    this.GetOperationName(methodName),
                    "Retry polling {0} operation status for subnet '{1}'. Async operation status: '{2}'.",
                    actionName,
                    subnetName,
                    asyncOperationResult?.Status);

                // Re-try after 2 seconds of delay if the retry interval isn't specified
                Thread.Sleep(TimeSpan.FromSeconds(PreparePollingIntervalSeconds));
            }

            return asyncOperationResult;
        }

        /// <summary>
        /// Gets the virtual network with subnets.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="vnetId">The virtual network id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<VirtualArmResource> GetVirtualNetwork(
            string authenticationTenantId,
            string vnetId,
            string resourceProviderNamespace)
        {
            Uri requestUri = UriTemplateEngine.GetVirtualNetworkUri(
                this.FrontdoorEndpointUri,
                vnetId,
                VirtualNetworkInjectionOperationsApiVersionKey);

            var vnetJsonResource = await this
                .GetResourcesFromFrontdoor<JObject>(
                    authenticationTenantId,
                    requestUri,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            return vnetJsonResource?.ToObject<VirtualArmResource>();
        }

        /// <summary>
        /// Gets the AML Workspace.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="amlWorkspaceId">The AML Workspace ID.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<JObject> GetAmlWorkspace(
            string authenticationTenantId,
            string amlWorkspaceId,
            string resourceProviderNamespace)
        {
            Uri requestUri = UriTemplateEngine.GetAmlWorkspaceUri(
                this.FrontdoorEndpointUri,
                amlWorkspaceId,
                AmlWorkspaceOperationsApiVersion);

            try
            {
                return await this
                    .GetResourcesFromFrontdoor<JObject>(
                        authenticationTenantId,
                        requestUri,
                        resourceProviderNamespace)
                    .ConfigureAwait(false);
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName: "FrontdoorEngine.GetAmlWorkspace",
                    format: "GetAmlWorkspace Failed - Request Uri: {0}. Exception: {1}. {2}",
                    arg0: requestUri,
                    arg1: exception.Message,
                    arg2: Utilities.FlattenException(exception));

                throw;
            }
        }

        /// <summary>
        /// Call Azure Resource Manager to PUT service endpoints on subnet
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="requestBody">The subnet request body</param>
        public async Task PutServiceEndpointsOnSubnet(
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            SubnetDefinition requestBody)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName, $"[Uri:{HttpMethod.Put} {requestUri.AbsoluteUri}] [RequestBody: {requestBody}]");

            try
            {
                var subnetDefinition = await AsyncRetry.Retry(
                    async () => await this
                    .CallFrontdoor<SubnetDefinition>(
                        HttpMethod.Put,
                        requestUri,
                        resourceProviderNamespace,
                        tenantId,
                        requestBody),
                    ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                    TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                    isRetryable: ex => ex.IsConnectivityException()
                    || (ex is ServerErrorResponseMessageException
                    && (((ServerErrorResponseMessageException)ex).HttpStatus.IsRetryableResponse()
                    || ProviderConstants.MicrosoftNetwork.RetryableErrorCodes.ContainsInsensitively(((ServerErrorResponseMessageException)ex).ErrorCode.ToString()))),
                    errorAction: (Exception exception) =>
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"Attempt to put service endpoint on subnet '{requestUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{Utilities.FlattenException(exception)}'.");
                    })
                    .ConfigureAwait(false);

                if (subnetDefinition == null)
                {
                    this.EventSource.Error(
                        operationName,
                        $"Enable service endpoint failed for subnet '{requestUri}'");
                    throw new ServerErrorResponseMessageException(
                        HttpStatusCode.InternalServerError,
                        ErrorResponseCode.InternalServerError.ToString(),
                        ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                }
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Exception occured during enabling service endpoints of the subnet '{requestUri}' in PublisherTenantId: {tenantId}. Error: {Utilities.FlattenException(exception)}");
                throw;
            }
        }

        /// <summary>
        /// Handles error response during prepare.
        /// </summary>
        /// <param name="virtualNetworkInjectionOperation">The virtual network injection operation</param>
        /// <param name="response">The response message.</param>
        /// <param name="subnetName">The subnet name.</param>
        /// <param name="requestType">The request type.</param>
        private async Task HandleVirtualNetworkInjectionOperationErrorResponse(VirtualNetworkInjectionOperation virtualNetworkInjectionOperation, HttpResponseMessage response, string subnetName, HttpMethod requestType)
        {
            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                "Handling error response while {0} subnet '{1}'. Request type: '{2}', Status code: '{3}'",
                virtualNetworkInjectionOperation.ToString(),
                subnetName,
                requestType.Method,
                response.StatusCode);

            ServerErrorResponseMessage errorResponseMessage = await this.GetServerErrorResponseMessage(response)
                .ConfigureAwait(false);

            bool isServerErrorResponseMessage = errorResponseMessage?.Error != null && !string.IsNullOrWhiteSpace(errorResponseMessage.Error.Code);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                "Server response error. Error code: '{0}', Message: '{1}'",
                isServerErrorResponseMessage ? errorResponseMessage.Error.Code : ErrorResponseCode.InternalServerError.ToString(),
                isServerErrorResponseMessage ? errorResponseMessage.Error.Message : ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));

            if (isServerErrorResponseMessage)
            {
                string errorMessage = !string.IsNullOrWhiteSpace(errorResponseMessage.Error.Message)
                    ? errorResponseMessage.Error.Message
                    : ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId);

                throw new VirtualNetworkInjectionOperationException(
                    response.StatusCode,
                    errorResponseMessage.Error.Code,
                    Utilities.GetPrepareSubnetErrorMessage(virtualNetworkInjectionOperation, subnetName, errorMessage));
            }

            throw new VirtualNetworkInjectionOperationException(
                HttpStatusCode.InternalServerError,
                ErrorResponseCode.InternalServerError.ToString(),
                Utilities.GetPrepareSubnetErrorMessage(virtualNetworkInjectionOperation, subnetName, ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId)));
        }

        /// <summary>
        /// Call Azure Resource Manager for PUT azure asynchronous operation
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="requestBody">The request body</param>
        public async Task<AzureAyncResponse> PutPrivateLinkServiceProxy(
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            JToken requestBody)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName, $"[Uri:{HttpMethod.Put} {requestUri.AbsoluteUri}] {Environment.NewLine}[RequestBody: {requestBody}]");

            //Get AAD TenantId by making GET Call
            tenantId = await GetTenantIdAsync(requestUri, resourceProviderNamespace, tenantId).ConfigureAwait(false);

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId)
                .ConfigureAwait(false);

            using (var response = await AsyncRetry.Retry(
                    () => this.FrontdoorClient.CallFrontdoorService(
                                    HttpMethod.Put,
                                    requestUri,
                                    authenticationToken.AccessToken,
                                    requestBody),
                    FrontdoorEngine.PrivateLinkMaxRetries,
                    FrontdoorEngine.PrivateLinkDefaultRetryInterval,
                    isRetryable: ex => ex.IsConnectivityException()
                                       || (ex is ServerErrorResponseMessageException
                                           && ((ServerErrorResponseMessageException)ex).HttpStatus.IsRetryableResponse()),
                    errorAction: (Exception exception) =>
                    {
                        this.EventSource.Debug(operationName, $"Attempt to PUT '{requestUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{Utilities.FlattenException(exception)}'.");
                    })
                .ConfigureAwait(false))
            {
                this.EventSource.Debug(operationName, $"[Uri:{HttpMethod.Put} {requestUri.AbsoluteUri}] [Response = {response?.StatusCode}] [ResponseHeaders: {response?.Headers}] [Response: {(response?.Content != null ? await response.Content.ReadAsStringAsync() : "None")}");

                if (response?.StatusCode == HttpStatusCode.Created || response?.StatusCode == HttpStatusCode.Accepted || response?.StatusCode == HttpStatusCode.OK)
                {
                    return new AzureAyncResponse
                    {
                        AzureAsyncOperationUri = new Uri(response.Headers.GetValues(RequestCorrelationContext.HeaderAzureAsyncOperation).First()),
                        RetryAfter = response.Headers.RetryAfter?.Delta,
                        Content = response.Content
                    };
                }

                if (response == null || response.StatusCode.IsSuccessfulRequest())
                {
                    return null;
                }

                if (response.StatusCode == HttpStatusCode.NotFound)
                {
                    throw new ErrorResponseMessageException(
                        httpStatus: HttpStatusCode.NotFound,
                        errorCode: ErrorResponseCode.ResourceNotFound,
                        errorMessage: ErrorResponseMessages.ResourceNotFound.ToLocalizedMessage());
                }

                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(false);

                this.EventSource.Debug(
                    operationName,
                    $"IsSuccessfulRequest: {response.StatusCode.IsSuccessfulRequest()}. Error Code: {errorResponseMessage?.Error?.Code}. Error Message: {errorResponseMessage?.Error?.Message}");

                throw new ServerErrorResponseMessageException(
                    response.StatusCode,
                    errorResponseMessage?.Error?.Code ?? ErrorResponseCode.InternalServerError.ToString(),
                    errorResponseMessage?.Error?.Message ?? ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
            }
        }

        #endregion

        #region Storage

        /// <summary>
        /// Updates the encryption settings on storage with BYOK asynchronously.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="managedResourceGroupName">Name of the managed resource group.</param>
        /// <param name="storageAccountName">Name of the storage account.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="storageUpdateRequest">The storage update request.</param>
        public async Task<HttpResponseMessage> UpdateStorageEncryptionWithByok(string subscriptionId, string managedResourceGroupName, string storageAccountName, string authenticationTenantId, string resourceProviderNamespace, StorageDefinition storageUpdateRequest)
        {
            var storageUpdateResourceUri = UriTemplateEngine.GetStorageGetPropertiesRequestUri(this.FrontdoorEndpointUri, subscriptionId, managedResourceGroupName, storageAccountName, this.StorageApiVersion);
            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Updating Storage Account {storageAccountName} with custom encryption.");

            return await this
                .PatchStorageResource(
                    storageUpdateResourceUri,
                    resourceProviderNamespace,
                    authenticationTenantId,
                    storageUpdateRequest)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Patches the storage resource.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="request">The request.</param>
        public async Task<HttpResponseMessage> PatchStorageResource(Uri requestUri,
            string resourceProviderNamespace,
            string authenticationTenantId,
            StorageDefinition requestBody)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(
                operationName,
                $"Patching Storage Resource in authenticationTenant: '{authenticationTenantId}'," +
                $" in resourceProviderNamespace '{resourceProviderNamespace}' with requestUri {requestUri}");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            this.EventSource.Debug("FrontdoorEngine.PatchStorageResource", $"Patch Storage Request Body: '{requestBody}'");

            using (var response = await AsyncRetry.Retry(
                           () => this.FrontdoorClient
                .CallFrontdoorService(
                    method: new HttpMethod("PATCH"),
                    requestUri: requestUri,
                    requestBody: requestBody,
                    accessToken: authenticationToken.AccessToken,
                    userAgent: resourceProviderNamespace),
                           ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                           TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                           isRetryable: ex =>
                               ex.IsConnectivityException() ||
                               (ex is ServerErrorResponseMessageException exception &&
                                exception.HttpStatus.IsRetryableResponse()),
                           errorAction: (Exception exception) =>
                           {
                               this.EventSource.Debug(operationName,
                                   $"Attempt to PATCH '{requestUri.AbsoluteUri}' failed. Framework will retry in " +
                                   $"{ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                           })
                       .ConfigureAwait(false))
            {
                if (response.StatusCode.IsSuccessfulRequest())
                {
                    this.EventSource.Debug(
                        operationName,
                        $"Successfully patched storage account. Storage Response : " +
                        $"{await response.Content.ReadAsStringAsync().ConfigureAwait(false)}");
                    return response;
                }

                var errorResponseMessage = await this
                .GetErrorResponseMessage(response)
                .ConfigureAwait(continueOnCapturedContext: false);

                this.EventSource.Error(
                    operationName,
                    $"IsSuccessfulRequest: {response.StatusCode.IsSuccessfulRequest()}." +
                    $" Error Code: {errorResponseMessage?.Error?.Code}." +
                    $" Error Message: {errorResponseMessage?.Error?.Message}");

                if (errorResponseMessage != null)
                {
                    throw new StorageOperationException(
                        httpStatus: response.StatusCode,
                        errorCode: errorResponseMessage.Error?.Code,
                        errorMessage: errorResponseMessage.Error?.Message);
                }

                throw new StorageOperationException(
                    httpStatus: HttpStatusCode.InternalServerError,
                    errorCode: ErrorResponseCode.InternalServerError.ToString(),
                    errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
            }
        }

        /// <summary>
        /// Check if the name of the dbfs account is available to use or not.
        /// </summary>
        /// <param name="authenticationTenantId">AAD tenantId to get token</param>
        /// <param name="subscriptionId">customer subscription Id</param>
        /// <param name="parameters">workspace entity parameters</param>
        /// <param name="resourceProviderNamespace">resource provider namespace</param>
        /// <returns></returns>
        public async Task CheckDbfsAccountNameAvailability(
                    string authenticationTenantId,
                    string subscriptionId,
                    InsensitiveDictionary<JToken> parameters,
                    string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var dbfsAccountName = parameters.ContainsKey(ProviderConstants.Databricks.StorageAccountNameParameter) == true ?
                parameters[ProviderConstants.Databricks.StorageAccountNameParameter].Value<string>("value") : string.Empty;

            this.EventSource.Debug(operationName, $"dbfsAccountName: '{dbfsAccountName}'");

            if (string.IsNullOrWhiteSpace(dbfsAccountName))
            {
                return;
            }

            Uri requestUri = UriTemplateEngine.CheckStorageAccountNameAvailability(
                    this.FrontdoorEndpointUri, subscriptionId, this.StorageApiVersion);

            try
            {
                var authenticationToken = await this
                    .GetAuthenticationTokenFromCache(
                        authenticationTenantId)
                    .ConfigureAwait(false);

                var requestBody = new JObject
                {
                    { ProviderConstants.Storage.NameParameter,  dbfsAccountName},
                    { ProviderConstants.Storage.ParameterType,  ProviderConstants.Storage.StorageAccountsType}
                };

                this.EventSource.Debug(operationName,
                    $"Checking availability of {dbfsAccountName} name for creating new account. " +
                    $"RequestUri: {requestUri.AbsoluteUri}, RequestBody: {requestBody}");

                using (var response = await this.FrontdoorClient
                    .CallFrontdoorService(
                        HttpMethod.Post,
                        requestUri,
                        authenticationToken.AccessToken,
                        requestBody)
                    .ConfigureAwait(false))
                {
                    if (response == null || response.StatusCode != HttpStatusCode.OK)
                    {
                        this.EventSource.Debug(operationName,
                            $"Found error - ResponseCode: {response.StatusCode}");

                        var errorResponseMessage = await this
                                .GetErrorResponseMessage(response)
                                .ConfigureAwait(false);

                        throw new ServerErrorResponseMessageException(
                            HttpStatusCode.Conflict,
                            ErrorResponseCode.CheckDbfsAccountNameFailed.ToString(),
                            ErrorResponseMessages.CheckDbfsAccountNameFailed.ToLocalizedMessage(dbfsAccountName, errorResponseMessage));
                    }

                    this.EventSource.Debug(operationName,
                    $"Completed checking availability of {dbfsAccountName} name for creating new account.");

                    var responseContent = await response.Content
                        .ReadAsJsonAsync<JObject>(MediaTypeFormatter)
                        .ConfigureAwait(false);

                    responseContent.TryGetValue(ProviderConstants.Storage.NameAvailableParameter,
                        StringComparison.OrdinalIgnoreCase, out JToken isNameAvailable);

                    if (bool.Parse(isNameAvailable.ToString()) == false)
                    {
                        throw new ErrorResponseMessageException(
                            HttpStatusCode.BadRequest,
                            ErrorResponseCode.dbfsAccountNameNotAvailable,
                            ErrorResponseMessages.dbfsAccountNameNotAvailable.ToLocalizedMessage(dbfsAccountName));
                    }
                }
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Checking name availability using '{requestUri}' with tenant '{authenticationTenantId}' is failed." +
                    $" Exception: {Utilities.FlattenException(exception)} & ResponseCode: {exception.HttpStatus}");
                throw;
            }
        }

        /// <summary>
        /// Gets containers present in dbfs account except UC Container.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="dbfsResourceId">Dbfs Account Resource Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<IEnumerable<StorageContainer>> GetDbfsContainers(
            string authenticationTenantId,
            string dbfsResourceId,
            string resourceProviderNamespace)
        {
            var containersUri = UriTemplateEngine.ListBlobContainersTemplate(
                this.FrontdoorEndpointUri,
                dbfsResourceId,
                this.StorageApiVersion);

            var dbfsContainers = await this
                            .GetResourcesFromFrontdoor<StorageContainerList>(
                                authenticationTenantId,
                                containersUri,
                                resourceProviderNamespace)
                            .ConfigureAwait(false);

            if (dbfsContainers == null)
            {
                return Enumerable.Empty<StorageContainer>();
            }

            return dbfsContainers.Value.CoalesceEnumerable().Where(
                            container => !container.Name.Equals(
                                ProviderConstants.Databricks.UcStorageContainer,
                                StringComparison.CurrentCultureIgnoreCase));
        }

        /// <summary>
        /// Deletes a container in storage account.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="dbfsResourceId">Dbfs Account Resource Id.</param>
        /// <param name="containerName">Dbfs Container Name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public Task<Uri> DeleteStorageAccountContainer(
            string authenticationTenantId,
            string dbfsResourceId,
            string containerName,
            string resourceProviderNamespace)
        {
            var containerResourceUri = UriTemplateEngine.DeleteStorageContainerTemplate(
                baseUri: this.FrontdoorEndpointUri,
                dbfsResourceId: dbfsResourceId,
                containerName: containerName,
                storageApiVersion: this.StorageApiVersion);

            return this.ExecuteDeleteResourceOperation<JObject>(
                authenticationTenantId: authenticationTenantId,
                httpMethod: HttpMethod.Delete,
                requestUri: containerResourceUri,
                resourceProviderNamespace: resourceProviderNamespace,
                requestBody: null);
        }

        #endregion

        #region Managed Disks

        /// <summary>
        /// Get the operation status for disk encryption set operation.
        /// </summary>
        /// <param name="diskEncryptionSetOperation">The disk encryption set operation being performed on MRG.</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="diskEncryptionSetId">The disk encryption set ID.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="asyncTrackingUri">The async tracking URI</param>
        public async Task<DiskEncryptionSetOperationResponse> GetDiskEncryptionSetOperationStatus(
            DiskEncryptionSetOperation diskEncryptionSetOperation,
            string authenticationTenantId,
            string diskEncryptionSetId,
            string resourceProviderNamespace,
            Uri asyncTrackingUri)
        {
            HttpResponseMessage response = null;

            string actionName = diskEncryptionSetOperation.ToString();
            string methodName = Utilities.GetAsyncMethodName();

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    authenticationTenantId,
                    forceRefresh: true)
                .ConfigureAwait(false);

            // Disk encryption set operation result should be ideally available in less than 5 minutes. Exit the loop if it is more.
            // TODO:1917116 : Remove the while loops for polling and use the Postpone Job instead
            DateTime start = DateTime.UtcNow;
            while (DateTime.UtcNow.Subtract(start).TotalMinutes < 5)
            {
                response = await AsyncRetry.Retry(
                        () => this.FrontdoorClient.CallFrontdoorService(
                            HttpMethod.Get,
                            asyncTrackingUri,
                            authenticationToken.AccessToken),
                        3,
                        TimeSpan.FromSeconds(PreparePollingIntervalSeconds))
                    .ConfigureAwait(false);

                this.EventSource.Debug(
                    methodName,
                    "Polling {0} operation status for the disk encryption set '{1}' - Status code: '{2}', Response: {3}",
                    actionName,
                    diskEncryptionSetId,
                    response?.StatusCode,
                    response?.Content?.ReadAsStringAsync().Result);

                if (response != null && response.StatusCode.IsSuccessfulRequest() &&
                    response.StatusCode != HttpStatusCode.Accepted)
                {
                    break;
                }

                if (response != null && !response.StatusCode.IsSuccessfulRequest())
                {
                    // Throw exception when the response is not successful or the async operation status is either failed or canceled.
                    await this.HandleDiskEncryptionSetOperationErrorResponse(
                        diskEncryptionSetOperation,
                        response,
                        diskEncryptionSetId,
                        HttpMethod.Get)
                        .ConfigureAwait(false);
                }

                this.EventSource.Debug(
                    this.GetOperationName(methodName),
                    "Retry polling {0} operation status for disk encryption set '{1}'. Async operation status: '{2}'.",
                    actionName,
                    diskEncryptionSetId,
                    response?.StatusCode);

                // Re-try after 2 seconds of delay if the retry interval isn't specified
                Thread.Sleep(TimeSpan.FromSeconds(PreparePollingIntervalSeconds));
            }

            return new DiskEncryptionSetOperationResponse
            {
                DiskEncryptionSetDefinition = await response?.Content?.ReadAsJsonAsync<DiskEncryptionSetDefinition>(MediaTypeFormatter),
                Status = (response != null
                            && response.StatusCode.IsSuccessfulRequest() &&
                            response.StatusCode != HttpStatusCode.Accepted)
                                ? OperationStatus.Success
                                : OperationStatus.Failed
            };
        }

        /// <summary>
        /// Creates the disk encryption set on with CMK asynchronously.
        /// </summary>
        /// <param name="managedResourceGroupId">Name of the managed resource group.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="subscriptionId">The subscription ID.</param>
        /// <param name="diskEncryptionSetCreateRequest">The disk encryption set create request.</param>
        public async Task<HttpResponseMessage> CreateManagedDiskEncryptionSetWithCmk(
            string managedResourceGroupId,
            string authenticationTenantId,
            string resourceProviderNamespace,
            string subscriptionId,
            DiskEncryptionSetDefinition diskEncryptionSetCreateRequest)
        {
            var diskEncryptionSetName = Utilities.GenerateUniqueDESName(managedResourceGroupId, subscriptionId);
            var diskEncryptionSetResourceUri = UriTemplateEngine.GetDiskEncryptionSetPutRequestUri(
                this.FrontdoorEndpointUri,
                managedResourceGroupId,
                diskEncryptionSetName,
                ProviderConstants.Compute.DiskEncryptionSetApiVersion);

            this.EventSource.Debug(
                operationName: this.GetOperationName(Utilities.GetAsyncMethodName()),
                format: "Creating Disk Encryption Set in Managed Resource Group with CMK with resourceProviderNamespace '{0}' and requestUri {1}",
                arg0: resourceProviderNamespace,
                arg1: diskEncryptionSetResourceUri.ToString());

            return await this
                .PutDiskEncryptionSetRequest(
                    diskEncryptionSetResourceUri,
                    resourceProviderNamespace,
                    authenticationTenantId,
                    diskEncryptionSetCreateRequest)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Creates a Put Request for the Disk Encryption Set in the managed resource group.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="requestBody">The request body.</param>
        public async Task<HttpResponseMessage> PutDiskEncryptionSetRequest(
            Uri requestUri,
            string resourceProviderNamespace,
            string authenticationTenantId,
            DiskEncryptionSetDefinition requestBody)
        {

            this.EventSource.Debug(
                operationName: "FrontdoorEngine.PutDiskEncryptionSetRequest",
                format: "Put Disk Encryption Set Resource in managed resource group with requestUri: {0}",
                arg0: requestUri.ToString());

            this.EventSource.Debug("FrontdoorEngine.PutDiskEncryptionSetRequest", $"Put Disk Encryption Set Request Body: '{requestBody.ToJson()}'");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await this.FrontdoorClient
                .CallFrontdoorService(
                    method: HttpMethod.Put,
                    requestUri: requestUri,
                    requestBody: requestBody,
                    accessToken: authenticationToken.AccessToken,
                    userAgent: resourceProviderNamespace)
                .ConfigureAwait(continueOnCapturedContext: false))
            {
                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    this.EventSource.Debug(
                        operationName: "FrontdoorEngine.PutDiskEncryptionSetRequest",
                        format: "IsSuccessfulRequest: {0}. Error Code: {1}. Error Message: {2}",
                        arg0: response.StatusCode.IsSuccessfulRequest(),
                        arg1: errorResponseMessage?.Error?.Code,
                        arg2: errorResponseMessage?.Error?.Message);

                    if (errorResponseMessage != null)
                    {
                        throw new DiskEncryptionSetOperationException(
                            httpStatus: response.StatusCode,
                            errorCode: errorResponseMessage.Error.Code,
                            errorMessage: errorResponseMessage.Error.Message);
                    }
                    else
                    {
                        throw new DiskEncryptionSetOperationException(
                            httpStatus: HttpStatusCode.InternalServerError,
                            errorCode: ErrorResponseCode.InternalServerError.ToString(),
                            errorMessage: ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }
                }
                else
                {
                    this.EventSource.Debug(
                        operationName: "FrontdoorEngine.PutDiskEncryptionSetRequest",
                        format: "Successfully initialised creation of Disk Encryption Set with RequestMessage: '{0}'",
                        arg0: await response.Content.ReadAsStringAsync().ConfigureAwait(false));

                    return response;
                }
            }
        }

        /// <summary>
        /// Gets the disk encryption set in the MRG.
        /// </summary>
        /// <param name="managedResourceGroupId">Name of the managed resource group.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="subscriptionId">The subscription ID.</param>
        /// <param name="requestBody">The disk encryption set get request body.</param>
        public async Task<DiskEncryptionSetOperationResponse> GetManagedDiskEncryptionSet(
            string managedResourceGroupId,
            string authenticationTenantId,
            string resourceProviderNamespace,
            string subscriptionId)
        {
            var diskEncryptionSetName = Utilities.GenerateUniqueDESName(managedResourceGroupId, subscriptionId);
            var diskEncryptionSetRequestUri = UriTemplateEngine.GetDiskEncryptionSetPutRequestUri(
                this.FrontdoorEndpointUri,
                managedResourceGroupId,
                diskEncryptionSetName,
                ProviderConstants.Compute.DiskEncryptionSetApiVersion);

            this.EventSource.Debug(
                operationName: this.GetOperationName(Utilities.GetAsyncMethodName()),
                format: "Retrieving Disk Encryption Set in Managed Resource Group with CMK with resourceProviderNamespace '{0}' and requestUri {1}",
                arg0: resourceProviderNamespace,
                arg1: diskEncryptionSetRequestUri.ToString());

            return await this
                .GetDiskEncryptionSet(
                    diskEncryptionSetRequestUri,
                    resourceProviderNamespace,
                    authenticationTenantId
                )
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Creates a Get Request for the Disk Encryption Set in the managed resource group.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        public async Task<DiskEncryptionSetOperationResponse> GetDiskEncryptionSet(
            Uri requestUri,
            string resourceProviderNamespace,
            string authenticationTenantId)
        {
            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            var response = await AsyncRetry.Retry(
                        () => this.GetResourcesFromFrontdoor<DiskEncryptionSetDefinition>(
                            authenticationTenantId,
                            requestUri,
                            resourceProviderNamespace),
                        FrontdoorEngine.DBWorkspaceNotificationRetryCount,
                        TimeSpan.FromSeconds(FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs),
                        isRetryable: ex =>
                            ex.IsConnectivityException() ||
                            (ex is ServerErrorResponseMessageException exception &&
                             exception.HttpStatus.IsRetryableResponse()),
                        errorAction: (Exception exception) =>
                        {
                            this.EventSource.Debug(
                                "GetDiskEncryptionSet",
                                $"Attempt to get encryptionset Id failed. Framework will retry in {FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs} seconds.Exception details: '{Utilities.FlattenException(exception)}'");
                        })
                    .ConfigureAwait(false);

            return new DiskEncryptionSetOperationResponse
            {
                DiskEncryptionSetDefinition = response,
                Status = response != null
                         && response.Properties["provisioningState"].Value<string>() == ProvisioningState.Succeeded.ToString()
                             ? OperationStatus.Success
                             : OperationStatus.Failed
            };
        }

        /// <summary>
        /// Handles error response during disk encryption set operation.
        /// </summary>
        /// <param name="diskEncryptionSetOperation">The disk encryption set operation</param>
        /// <param name="response">The response message.</param>
        /// <param name="diskEncryptionSetId">The disk encryption set id.</param>
        /// <param name="requestType">The request type.</param>
        private async Task HandleDiskEncryptionSetOperationErrorResponse(
            DiskEncryptionSetOperation diskEncryptionSetOperation,
            HttpResponseMessage response,
            string diskEncryptionSetId,
            HttpMethod requestType)
        {
            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                "Handling error response while {0} of disk encryption set: '{1}'. Request type: '{2}', Status code: '{3}'",
                diskEncryptionSetOperation.ToString(),
                diskEncryptionSetId,
                requestType.Method,
                response.StatusCode);

            ServerErrorResponseMessage errorResponseMessage = await this.GetServerErrorResponseMessage(response)
                .ConfigureAwait(false);

            bool isServerErrorResponseMessage = errorResponseMessage?.Error != null && !string.IsNullOrWhiteSpace(errorResponseMessage.Error.Code);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                "Server response error. Error code: '{0}', Message: '{1}'",
                isServerErrorResponseMessage ? errorResponseMessage.Error.Code : ErrorResponseCode.InternalServerError.ToString(),
                isServerErrorResponseMessage ? errorResponseMessage.Error.Message : ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));

            if (isServerErrorResponseMessage)
            {
                string errorMessage = !string.IsNullOrWhiteSpace(errorResponseMessage.Error.Message)
                    ? errorResponseMessage.Error.Message
                    : ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId);

                throw new DiskEncryptionSetOperationException(
                    response.StatusCode,
                    errorResponseMessage.Error.Code,
                    Utilities.GetDiskEncryptionSetOperationErrorMessage(diskEncryptionSetOperation, diskEncryptionSetId, errorMessage));
            }

            throw new DiskEncryptionSetOperationException(
                HttpStatusCode.InternalServerError,
                ErrorResponseCode.InternalServerError.ToString(),
                Utilities.GetDiskEncryptionSetOperationErrorMessage(diskEncryptionSetOperation, diskEncryptionSetId, ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId)));
        }

        /// <summary>
        /// Checks if disks exist in resource group
        /// </summary>
        /// <param name="authenticationTenantId"></param>
        /// <param name="resourceProviderNamespace"></param>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroupName"></param>
        /// <param name="apiVersion"></param>
        /// <returns>The DisksExistInResourceGroupResponse </returns>
        public async Task<DisksExistInResourceGroupResponse> CheckIfDisksExistInResourceGroup(
           string authenticationTenantId,
           string resourceProviderNamespace,
           string subscriptionId,
           string resourceGroupName,
           string apiVersion)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var uri = UriTemplateEngine.GetDisksInResourceGroupUriTemplate(this.FrontdoorEndpointUri, subscriptionId, resourceGroupName, apiVersion);

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            using (var response = await AsyncRetry.Retry(
                           () => this.FrontdoorClient
                               .CallFrontdoorService(
                                   method: HttpMethod.Get,
                                   requestUri: uri,
                                   accessToken: authenticationToken.AccessToken),
                           ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                           TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                           isRetryable: ex =>
                               ex.IsConnectivityException() ||
                               (ex is ServerErrorResponseMessageException exception &&
                                exception.HttpStatus.IsRetryableResponse()),
                           errorAction: (Exception exception) =>
                           {
                               this.EventSource.Debug(operationName,
                                   $"Attempt to GET '{uri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                           })
                       .ConfigureAwait(false))
            {
                if (response.StatusCode.IsSuccessfulRequest())
                {
                    var disksExistInResourceGroup = response.Content.ReadAsJsonAsync<JToken>(MediaTypeFormatter).Result["value"]?.Count() > 0;

                    this.EventSource.Debug(
                    operationName,
                    $"disksExistInResourceGroup {uri.AbsoluteUri} : {disksExistInResourceGroup}");

                    return new DisksExistInResourceGroupResponse { DisksExistsInResourceGroup = disksExistInResourceGroup, Status = OperationStatus.Success };

                }

                var errorResponseMessage = await this
                    .GetErrorResponseMessage(response)
                    .ConfigureAwait(continueOnCapturedContext: false);

                return new DisksExistInResourceGroupResponse
                {
                    ErrorResponseMessage = errorResponseMessage,
                    Status = OperationStatus.Failed
                };
            }
        }

        #endregion

        #region Virtual Machines

        /// <summary>
        /// Checks if virtual machines exist in resource group
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="apiVersion">The api version.</param>
        /// <returns>Returns if virtual machines exist in the resource group.</returns>
        public async Task<bool> CheckIfVirtualMachineExistInResourceGroup(
           string authenticationTenantId,
           string resourceProviderNamespace,
           string subscriptionId,
           string resourceGroupName,
           string apiVersion)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            var uri = UriTemplateEngine.GetVirtualMachinesInResourceGroupUriTemplate(this.FrontdoorEndpointUri, subscriptionId, resourceGroupName, apiVersion);

            try
            {
                var authenticationToken = await this
                    .GetAuthenticationTokenFromCache(
                        tenantId: authenticationTenantId)
                    .ConfigureAwait(continueOnCapturedContext: false);

                using (var response = await AsyncRetry.Retry(
                               () => this.FrontdoorClient
                                   .CallFrontdoorService(
                                       method: HttpMethod.Get,
                                       requestUri: uri,
                                       accessToken: authenticationToken.AccessToken),
                               ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                               TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                               isRetryable: ex =>
                                   ex.IsConnectivityException() ||
                                   (ex is ServerErrorResponseMessageException exception &&
                                    exception.HttpStatus.IsRetryableResponse()),
                               errorAction: (Exception exception) =>
                               {
                                   this.EventSource.Debug(operationName,
                                       $"Attempt to GET '{uri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                               })
                           .ConfigureAwait(false))
                {
                    if (response.StatusCode.IsSuccessfulRequest())
                    {
                        var virtualMachinesExistInResource = response.Content.ReadAsJsonAsync<JToken>(MediaTypeFormatter).Result["value"]?.Count() > 0;

                        this.EventSource.Debug(
                        operationName,
                        $"virtualMachinesExistInResourceGroup {uri.AbsoluteUri} : {virtualMachinesExistInResource}");

                        return virtualMachinesExistInResource;
                    }

                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    throw new ServerErrorResponseMessageException(
                                response.StatusCode,
                                errorResponseMessage.Error?.Code ?? ErrorResponseCode.WorkspaceUpdateFailed.ToString(),
                                errorResponseMessage.ToJson() ?? ErrorResponseMessages.WorkspaceUpdateFailed.ToLocalizedMessage());
                }
            }
            catch (Exception exception)
            {
                this.EventSource.Debug(operationName,
                    $"Failed to retrieve virtual machines in MRG. Exception: {Utilities.FlattenException(exception)}");

                throw new ErrorResponseMessageException(
                            HttpStatusCode.InternalServerError,
                            ErrorResponseCode.WorkspaceUpdateFailed,
                            ErrorResponseMessages.WorkspaceUpdateFailed.ToLocalizedMessage());
            }
        }

        #endregion

        #region Feature

        /// <summary>
        /// Gets feature registration by name.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="featureName">The feature name</param>
        public async Task<FeatureDefinition> GetFeatureRegistrationByName(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace,
            string featureName)
        {
            var featureRegistrationsUri = UriTemplateEngine.GetFeatureRegistrationByNameRequestUri(
                this.FrontdoorEndpointUri,
                subscriptionId,
                resourceProviderNamespace,
                featureName,
                FrontdoorEngine.FeaturesApiVersion);

            return await this
                .GetResourcesFromFrontdoor<FeatureDefinition>(
                    authenticationTenantId,
                    featureRegistrationsUri,
                    resourceProviderNamespace)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Gets resource provider feature registrations for a subscription.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<ResponseWithContinuation<FeatureDefinition[]>> GetProviderFeatureRegistrations(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace)
        {
            var featureRegistrationsUri = UriTemplateEngine.GetProviderFeatureRegistrationsRequestUri(
                this.FrontdoorEndpointUri,
                subscriptionId,
                resourceProviderNamespace,
                FrontdoorEngine.FeaturesApiVersion);

            return await this
                .GetResourcesFromFrontdoor<ResponseWithContinuation<FeatureDefinition[]>>(
                    authenticationTenantId,
                    featureRegistrationsUri,
                    resourceProviderNamespace)
                .ConfigureAwait(false);
        }

        /// <summary>
        /// Gets list of registered features in given subscription.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<List<string>> GetRegisteredFeaturesInSubscription(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName, "Getting a list of feature registrations from the frontdoor engine");

            var features = await this
                .GetProviderFeatureRegistrations(
                    authenticationTenantId,
                    subscriptionId,
                    resourceProviderNamespace)
                .ConfigureAwait(false);

            var registeredFeatures = new List<string>();

            if (features != null && features.Value.Any())
            {
                foreach (var feature in features.Value)
                {
                    var featureRegistrationState = feature.Properties?.TryGetProperty<RegistrationState>(ProviderConstants.Features.StateProperty) ?? RegistrationState.NotSpecified;

                    if (featureRegistrationState != RegistrationState.Registered)
                    {
                        continue;
                    }

                    registeredFeatures.Add(feature.Name);
                }
            }

            this.EventSource.Debug(operationName, $" Total {registeredFeatures.Count} Features are Registered in the subscription {subscriptionId}," +
                $" Feature Names: {string.Join(",", registeredFeatures)}");

            return registeredFeatures;
        }

        #endregion

        #region ARM Frontdoor Client

        /// <summary>
        /// Gets the dbWorkspace object from DbBackend Service
        /// </summary>
        /// <param name="resourceId">Workspace resourceId</param>
        /// <param name="apiVersion">DbBackend service API version</param>
        /// <param name="resourceProviderNamespace">Databricks RP namespace</param>
        /// <param name="retryCount">No of times get request to be retried</param>
        /// <param name="retryInterval">Amount of time to be waited between retries</param>
        /// <returns></returns>
        public async Task<T> GetDbWorkspaceDetailsAsync<T>(
            Uri requestUri,
            string apiVersion,
            string resourceProviderNamespace,
            string audience,
            int retryCount,
            int retryInterval)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            T dbWorkspaceDetails = await AsyncRetry.Retry(
                    () => this.GetResourcesFromFrontdoor<T>(
                        RequestCorrelationContext.Current.GetHomeTenantId(),
                        requestUri,
                        resourceProviderNamespace,
                        audience),
                        retryCount,
                    TimeSpan.FromSeconds(retryInterval),
                    isRetryable: ex =>
                        ex.IsConnectivityException() ||
                        (ex is ServerErrorResponseMessageException exception &&
                         exception.HttpStatus.IsRetryableResponse()),
                    errorAction: (Exception exception) =>
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"Attempt to get DB workspace '{requestUri}' failed. " +
                            $"Framework will retry in {retryInterval} seconds." +
                            $"Exception details: '{Utilities.FlattenException(exception)}'.");
                    })
                .ConfigureAwait(false);

            this.EventSource.Debug(
                operationName,
                $"Queried dbworkspace resource from Databricks: {(dbWorkspaceDetails == null ? "null" : JToken.FromObject(dbWorkspaceDetails))}");

            return dbWorkspaceDetails;
        }


        /// <summary>
        /// Gets resource(s) from ARM Front door.
        /// </summary>
        /// <typeparam name="T">The type of authorization request body.</typeparam>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<T> GetResourcesFromFrontdoor<T>(
            string authenticationTenantId,
            Uri requestUri,
            string resourceProviderNamespace,
            string audience = null)
        {
            T resource = default;

            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            try
            {
                var authenticationToken = await this
                    .GetAuthenticationTokenFromCache(
                        authenticationTenantId, audience)
                    .ConfigureAwait(false);
                this.EventSource.Debug(
                                operationName,
                                $"[Common]START: GET from {requestUri} with authenticationTenantId '{authenticationTenantId}' and audience '{audience}'");
                using (var response = await this.FrontdoorClient
                    .CallFrontdoorService(
                        HttpMethod.Get,
                        requestUri,
                        authenticationToken.AccessToken)
                    .ConfigureAwait(false))
                {
                    if (response == null || response.StatusCode == HttpStatusCode.NotFound)
                    {
                        return default;
                    }

                    if (!response.StatusCode.IsSuccessfulRequest())
                    {
                        var errorResponseMessage = await this
                            .GetErrorResponseMessage(response)
                            .ConfigureAwait(false);

                        this.EventSource.Debug(
                                operationName,
                                $"[Common]Found error - ResponseCode: {response.StatusCode}");

                        if (errorResponseMessage != null)
                        {
                            this.EventSource.Debug(
                                operationName,
                                $"[Common]ErrorCode: {errorResponseMessage.Error.Code} | ErrorMessage: {errorResponseMessage.Error.Message}");

                            throw new ServerErrorResponseMessageException(
                                response.StatusCode,
                                errorResponseMessage.Error.Code,
                                errorResponseMessage.Error.Message);
                        }

                        throw new ServerErrorResponseMessageException(
                            response.StatusCode,
                            ErrorResponseCode.InternalServerError.ToString(),
                            ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                    }

                    resource = await response.Content
                        .ReadAsJsonAsync<T>(MediaTypeFormatter)
                        .ConfigureAwait(false);
                }
            }
            catch (ServerErrorResponseMessageException exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"[Common]The '{requestUri}' lookup using tenant '{authenticationTenantId}' is failed. Exception: {Utilities.FlattenException(exception)} & ResponseCode: {exception.HttpStatus}");

                if (exception.HttpStatus != HttpStatusCode.NotFound)
                {
                    throw;
                }
            }

            return resource;
        }

        /// <summary>
        /// Call Azure Resource Manager for any operation
        /// </summary>
        /// <typeparam name="T">The type of authorization response body.</typeparam>
        /// <param name="httpMethod">The HTTP Method</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders">additional header to pass</param>
        /// <returns>The response of the operation or throws exception</returns>
        public async Task<T> CallFrontdoor<T>(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            T requestBody,
            string audience = null,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName,
                $"[Common]START: Calling: {httpMethod} on {requestUri}" +
                $"ResourceProviderNamespace: '{resourceProviderNamespace}', " +
                $"TenantId: '{tenantId}', " +
                $"Audience: '{audience}', " +
                $"Uri: {requestUri.AbsoluteUri}, " +
                $"Headers: {(additionalHeaders != null ? string.Join(",", additionalHeaders.Select(h => $"{h.Key}={h.Value}")) : "none")}, " +
                $"RequestBody: {(requestBody == null ? null : JToken.FromObject(requestBody))}");
            try
            {
                var authenticationToken = await this
                    .GetAuthenticationTokenFromCache(
                        tenantId, audience)
                    .ConfigureAwait(false);

                using (var response = requestBody != null
                           ? await this
                               .FrontdoorClient
                               .CallFrontdoorService(
                                   httpMethod,
                                   requestUri,
                                   authenticationToken.AccessToken,
                                   requestBody,
                                   additionalHeaders: additionalHeaders)
                               .ConfigureAwait(false)
                           : await this
                               .FrontdoorClient
                               .CallFrontdoorService(
                                   httpMethod,
                                   requestUri,
                                   authenticationToken.AccessToken,
                                   additionalHeaders)
                               .ConfigureAwait(false))
                {
                    if (response.Content != null)
                    {
                        var responseContent = await response.Content
                            .ReadAsStringAsync()
                            .ConfigureAwait(false);

                        this.EventSource.Debug(
                            operationName,
                            $"[Common]Response - StatusCode: {response.StatusCode} IsSuccessfulRequest: {response.StatusCode.IsSuccessfulRequest()}. Content: {responseContent}");
                    }

                    if (!response.StatusCode.IsSuccessfulRequest())
                    {
                        if (httpMethod == HttpMethod.Delete && response.StatusCode == HttpStatusCode.NotFound)
                        {
                            this.EventSource.Debug(
                                operationName,
                                $"[Common]DELETE call was made on empty workspace, bypassing the 404 error response from Databricks");
                        }
                        else
                        {
                            var errorResponseMessage = await this
                                .GetErrorResponseMessage(response)
                                .ConfigureAwait(false);

                            this.EventSource.Debug(
                                operationName,
                                $"[Common]IsSuccessfulRequest: {response.StatusCode.IsSuccessfulRequest()}. Error Code: {errorResponseMessage?.Error?.Code}. Error Message: {errorResponseMessage?.Error?.Message}");

                            throw new ServerErrorResponseMessageException(
                                response.StatusCode,
                                errorResponseMessage?.Error?.Code ?? ErrorResponseCode.InternalServerError.ToString(),
                                errorResponseMessage?.Error?.Message ?? ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                        }
                    }

                    return await response.Content
                        .ReadAsJsonAsync<T>(MediaTypeFormatter)
                        .ConfigureAwait(false);
                }
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"[Common]The '{httpMethod}' '{requestUri}' failed. Exception: {Utilities.FlattenException(exception)}");

                throw;
            }
        }

        #endregion

        #region Databricks Credential Manager

        /// <summary>
        /// Call Databricks Credential Manager for any operation
        /// </summary>
        /// <typeparam name="T">The type of authorization response body.</typeparam>
        /// <param name="httpMethod">The HTTP Method</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="audience">AAD Audience for request.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders">Additional headers to be passed to the request</param>
        /// <returns>The response of the operation or throws exception</returns>
        public async Task CallDatabricksAsyncAccountApi<T>(
            HttpMethod httpMethod,
            Uri requestUri,
            string tenantId,
            string audience,
            T requestBody,
            InsensitiveDictionary<string> additionalHeaders = null)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            try
            {
                this.EventSource.Debug(operationName,
                    $"[Account Api]Calling: {httpMethod} on {requestUri} using auth tenant {tenantId} and audience {audience}");

                var authenticationToken = await this.GetAuthenticationTokenFromAad(tenantId, audience)
                        .ConfigureAwait(false);

                if (additionalHeaders == null)
                {
                    additionalHeaders = Utilities.GetDatabricksOnBehalfOfUserHeader();
                }

                this.EventSource.Debug(
                    operationName,
                    $"[Account Api]Calling Account Api with requestBody: {requestBody}");

                //TODO: Add retry for calling CM calls.
                using (var response = await this
                               .FrontdoorClient
                               .CallFrontdoorService(
                                   httpMethod,
                                   requestUri,
                                   authenticationToken.AccessToken,
                                   requestBody,
                                   additionalHeaders: additionalHeaders.ToArray())
                               .ConfigureAwait(false))
                {
                    if (!response.StatusCode.IsSuccessfulRequest())
                    {
                        var databricksErrorInfo = await response.Content
                            .ReadAsJsonAsync<DatabricksErrorInfo>(MediaTypeFormatter)
                            .ConfigureAwait(false);

                        this.EventSource.Debug(
                            operationName,
                            $"[Account Api]Response - StatusCode: {response.StatusCode}," +
                            $" IsSuccessfulRequest: {response.StatusCode.IsSuccessfulRequest()}." +
                            $" Content: {databricksErrorInfo}");

                        var responseCode = response.StatusCode == HttpStatusCode.Forbidden ? HttpStatusCode.PreconditionFailed : response.StatusCode;

                        throw new AccessConnectorErrorResponse(
                            responseCode,
                            databricksErrorInfo?.Code,
                            databricksErrorInfo?.Message);
                    }
                }
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"[Account Api]The '{httpMethod}' '{requestUri}' failed. Exception: {Utilities.FlattenException(exception)}");

                throw;
            }
        }
        #endregion

        #region Helper
        /// <summary>
        /// Gets error message from HTTP response.
        /// </summary>
        /// <param name="response">The HTTP response message.</param>
        private async Task<ErrorResponseMessage> GetErrorResponseMessage(HttpResponseMessage response)
        {
            var responseContent = await response.Content
                .ReadAsStringAsync()
                .ConfigureAwait(false);

            return responseContent.TryFromJson<ErrorResponseMessage>();
        }

        /// <summary>
        /// Gets error message from HTTP response.
        /// </summary>
        /// <param name="response">The HTTP response message.</param>
        private async Task<ServerErrorResponseMessage> GetServerErrorResponseMessage(HttpResponseMessage response)
        {
            var responseContent = await response.Content
                .ReadAsStringAsync()
                .ConfigureAwait(continueOnCapturedContext: false);

            return responseContent.TryFromJson<ServerErrorResponseMessage>();
        }

        /// <summary>
        /// Generates the query parameters.
        /// </summary>
        /// <param name="requestUri">The request uri.</param>
        /// <param name="apiVersion">The api version.</param>
        /// <param name="queryParameters">The query parameters.</param>
        private NameValueCollection GenerateQuery(Uri requestUri, NameValueCollection queryParameters = null)
        {
            var endpointQuery = requestUri.ParseQueryString();

            foreach (var queryParameter in (queryParameters ?? new NameValueCollection()).AllKeys)
            {
                endpointQuery[queryParameter] = queryParameters[queryParameter];
            }

            return endpointQuery;
        }

        /// <summary>
        /// Creates and returns a graph resource definition from a JObject.
        /// </summary>
        /// <param name="graphJObject">The JObject representing the graph resource.</param>
        private GraphPrincipalDetail GetGraphResourceDefinitionFromJObject(JObject graphJObject)
        {
            return new GraphPrincipalDetail
            {
                Id = graphJObject.GetValue("id")?.ToString(),
                DisplayName = graphJObject.GetValue("displayName")?.ToString(),
                Mail = String.IsNullOrWhiteSpace(graphJObject.GetValue("mail")?.ToString()) ? null : graphJObject.GetValue("mail")?.ToString(),
                UserPrincipalName = graphJObject.GetValue("userPrincipalName")?.ToString(),
                UserType = graphJObject.GetValue("userType")?.ToString(),
                PrincipalType = Utilities.GetPrincipalType(graphJObject.GetValue("@odata.type")?.ToString())
            };
        }

        /// <summary>
        /// Handle Exception Response
        /// </summary>
        /// <param name="requestUri">Host Name to determine Graph API or DB API</param>
        /// <param name="errorResponseBody">Response from Server</param>
        /// <param name="statusCode">Http Status Code</param>
        private IDatabricksErrorInfo HandleEndpointErrorResponse(Uri requestUri, String errorResponseBody, HttpStatusCode statusCode)
        {
            IDatabricksErrorInfo errorResponseMessage = default(IDatabricksErrorInfo);

            this.EventSource.Error("HandleEndpointErrorResponse", errorResponseBody);

            errorResponseMessage = Utilities.GetErrorResponseMessage<DatabricksErrorInfo>(errorResponseBody);

            if (errorResponseMessage?.Code == null && requestUri.Host.Equals(new Uri(Utilities.GraphApiEndpoint).Host, StringComparison.OrdinalIgnoreCase))
            {
                this.EventSource.Debug("HandleEndpointErrorResponse", "Deserialize Graph API Error");

                var msGraphError = Utilities.GetErrorResponseMessage<MsGraphErrorResponse>(errorResponseBody);
                if (msGraphError != null && msGraphError.Error != null)
                {
                    errorResponseMessage = msGraphError.Error;
                }
            }

            return errorResponseMessage;
        }

        #endregion

        #region DB Workspace Notification
        /// <summary>
        /// DB workspace Notifications with feature flag
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders"></param>
        /// <returns></returns>
        public async Task DBWorkspaceNotificationWrapper(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            string audience,
            bool useArm = true,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName,
                $"[Feature Flag selection]START: DB Workspace {httpMethod.Method} Notification on WorkspaceId: '{workspaceId}'" +
                $"Using {(useArm ? "ARM" : "AccountApi")}");
            if (useArm)
            {
                await this.DBWorkspaceNotificationWithARM(
                    httpMethod,
                    requestUri,
                    resourceProviderNamespace,
                    tenantId,
                    workspaceId,
                    requestBody,
                    additionalHeaders).ConfigureAwait(false);
            }
            else
            {
                await this.DBWorkspaceNotificationWithAccountApi(
                    httpMethod,
                    requestUri,
                    resourceProviderNamespace,
                    tenantId,
                    workspaceId,
                    requestBody,
                    audience,
                    additionalHeaders).ConfigureAwait(false);
            }

        }

        /// <summary>
        /// DB workspace Update Notifications with feature flag and polling response
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="audience">The audience for Account API</param>
        /// <param name="useArm">Whether to use ARM or Account API</param>
        /// <param name="baseUri">The base URI for Account API</param>
        /// <param name="additionalHeaders">Additional headers</param>
        /// <returns>Returns workspace update response with polling information</returns>
        public async Task<WorkspaceUpdateResponse> DBWorkspaceUpdateNotificationWrapper(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            string audience,
            bool useArm,
            Uri baseUri,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName,
                $"[Feature Flag selection]START: DB Workspace {httpMethod.Method} Update Notification on WorkspaceId: '{workspaceId}'" +
                $"Using {(useArm ? "ARM" : "AccountApi")}");

            if (useArm)
            {
                // For ARM, we don't need polling - just call the existing method
                await this.DBWorkspaceNotificationWithARM(
                    httpMethod,
                    requestUri,
                    resourceProviderNamespace,
                    tenantId,
                    workspaceId,
                    requestBody,
                    additionalHeaders).ConfigureAwait(false);

                // Return response indicating no polling needed for ARM
                return new WorkspaceUpdateResponse
                {
                    Status = OperationStatus.Success,
                    RequiresPolling = false
                };
            }
            else
            {
                // For Account API, call the notification and determine if polling is needed
                await this.DBWorkspaceNotificationWithAccountApi(
                    httpMethod,
                    requestUri,
                    resourceProviderNamespace,
                    tenantId,
                    workspaceId,
                    requestBody,
                    audience,
                    additionalHeaders).ConfigureAwait(false);

                // For Account API updates, we need to check if polling is required
                // This is a simplified approach - in a real scenario, you might want to
                // inspect the response headers or status to determine if polling is needed
                return new WorkspaceUpdateResponse
                {
                    Status = OperationStatus.Success,
                    RequiresPolling = true,
                    PollingState = AccountApiPollingState.WaitingForUpdate,
                    OperationId = workspaceId,
                    BaseUri = baseUri,
                    Audience = audience,
                    PollingIntervalSeconds = this.AccountApiPollingRetryInterval,
                    ResourceOperation = ProvisioningOperation.AccountApiPolling
                };
            }
        }

        /// <summary>
        /// To call Azure Resource Manager for DB workspace Notifications
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders"></param>
        /// <returns>Returns DBCS response or throws exception</returns>
        public async Task DBWorkspaceNotificationWithAccountApi(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            string audience,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName,
                $"[Account Api]START: DB Workspace {httpMethod.Method} Notification on WorkspaceId: '{workspaceId}', " +
                $"ResourceProviderNamespace: '{resourceProviderNamespace}', " +
                $"TenantId: '{tenantId}', " +
                $"Audience: '{audience}', " +
                $"Uri: {requestUri.AbsoluteUri}, " +
                $"Headers: {(additionalHeaders != null ? string.Join(",", additionalHeaders.Select(h => $"{h.Key}={h.Value}")) : "none")}, " +
                $"RequestBody: {requestBody}");

            try
            {
                var headers = additionalHeaders == null ? null : new InsensitiveDictionary<string>(
                                    additionalHeaders.ToDictionary(p => p.Key, p => p.Value));
                await AsyncRetry.Retry(
                    () => this
                            .CallDatabricksAsyncAccountApi<JToken>(
                                httpMethod,
                                requestUri,
                                tenantId,
                                audience,
                                requestBody,
                                headers
                                ),
                    FrontdoorEngine.DBWorkspaceNotificationRetryCount,
                    TimeSpan.FromSeconds(FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs),
                    isRetryable: ex => ex.IsConnectivityException()
                                       || (ex is ServerErrorResponseMessageException exception && exception.HttpStatus.IsRetryableResponse()),
                    errorAction: (Exception exception) =>
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"[Account Api]INTERIM ERROR: Attempt to DB Workspace {httpMethod.Method} Notification failed. Framework will retry in {FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs} seconds.Exception details: '{Utilities.FlattenException(exception)}'");
                    })
                .ConfigureAwait(false);

                this.EventSource.Debug(
                    operationName,
                    $"[Account Api]SUCCESS: DB Workspace {httpMethod.Method} Notification on WorkspaceId: '{workspaceId}'");
            }
            catch (ErrorResponseMessageException errorResponseMessageException)
            {
                this.EventSource.Error(
                    operationName,
                    $"[Account Api]Error Response:DB Workspace {httpMethod.Method} Notification failed on WorkspaceId: '{workspaceId}' in CustomerTenantId: '{tenantId}'. Exception: {Utilities.FlattenException(errorResponseMessageException)}");

                throw;
            }
            catch (ServerErrorResponseMessageException serverErrorResponseMessageException)
            {
                this.EventSource.Error(
                    operationName,
                    $"[Account Api]Server Error: DB Workspace {httpMethod.Method} Notification failed on WorkspaceId: '{workspaceId}' in CustomerTenantId: '{tenantId}'. Exception: {Utilities.FlattenException(serverErrorResponseMessageException)}");

                if (serverErrorResponseMessageException.HttpStatus == HttpStatusCode.NotFound)
                {
                    return;
                }

                throw;
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"[Account Api]Error: DB Workspace Notification {httpMethod.Method} failed on WorkspaceId: '{workspaceId}' in CustomerTenantId: {tenantId}. Error: {Utilities.FlattenException(exception)}");

                throw;
            }
        }

        /// <summary>
        /// To call Azure Resource Manager for DB workspace Notifications
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders"></param>
        /// <returns>Returns DBCS response or throws exception</returns>
        public async Task DBWorkspaceNotificationWithARM(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            this.EventSource.Debug(operationName, $"START: DB Workspace {httpMethod.Method} Notification on WorkspaceId: '{workspaceId}'. Uri:{httpMethod.Method} {requestUri.AbsoluteUri}] [RequestBody: {requestBody}");

            try
            {
                await AsyncRetry.Retry(
                    () => this
                            .CallFrontdoor<JToken>(
                                httpMethod,
                                requestUri,
                                resourceProviderNamespace,
                                tenantId,
                                requestBody,
                                additionalHeaders: additionalHeaders),
                    FrontdoorEngine.DBWorkspaceNotificationRetryCount,
                    TimeSpan.FromSeconds(FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs),
                    isRetryable: ex => ex.IsConnectivityException()
                                       || (ex is ServerErrorResponseMessageException exception && exception.HttpStatus.IsRetryableResponse()),
                    errorAction: (Exception exception) =>
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"INTERIM ERROR: Attempt to DB Workspace {httpMethod.Method} Notification failed. Framework will retry in {FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs} seconds.Exception details: '{Utilities.FlattenException(exception)}'");
                    })
                .ConfigureAwait(false);

                this.EventSource.Debug(
                    operationName,
                    $"SUCCESS: DB Workspace {httpMethod.Method} Notification on WorkspaceId: '{workspaceId}'");
            }
            catch (ErrorResponseMessageException errorResponseMessageException)
            {
                this.EventSource.Error(
                    operationName,
                    $"Error Response: DB Workspace {httpMethod.Method} Notification failed on WorkspaceId: '{workspaceId}' in CustomerTenantId: '{tenantId}'. Exception: {Utilities.FlattenException(errorResponseMessageException)}");

                throw;
            }
            catch (ServerErrorResponseMessageException serverErrorResponseMessageException)
            {
                this.EventSource.Error(
                    operationName,
                    $"Server Error: DB Workspace {httpMethod.Method} Notification failed on WorkspaceId: '{workspaceId}' in CustomerTenantId: '{tenantId}'. Exception: {Utilities.FlattenException(serverErrorResponseMessageException)}");

                if (serverErrorResponseMessageException.HttpStatus == HttpStatusCode.NotFound)
                {
                    return;
                }

                throw;
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Error: DB Workspace Notification {httpMethod.Method} failed on WorkspaceId: '{workspaceId}' in CustomerTenantId: {tenantId}. Error: {Utilities.FlattenException(exception)}");

                throw;
            }
        }

        /// <summary>
        /// Makes Databricks/Graph API Endpoint call.
        /// </summary>
        /// <typeparam name="TMessage">The request content type.</typeparam>
        /// <param name="tenantId">The tenant id to authenticate.</param>
        /// <param name="baseUri">The base uri.</param>
        /// <param name="requestPath">The relative path.</param>
        /// <param name="method">The http method.</param>
        /// <param name="message">The request message.</param>
        /// <param name="resource">AAD Target Resource ID or AAD Audience URL.The resource to get an AAD token for</param>
        /// <param name="requestHeaders">The request headers.</param>
        /// <param name="queryParameters">The query parameters.</param>
        public async Task<TMessage> CallEndpoint<TMessage>(
            string tenantId,
            string baseUri,
            string requestPath,
            HttpMethod method,
            TMessage message,
            string resource,
            InsensitiveDictionary<string> requestHeaders = null,
            NameValueCollection queryParameters = null)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(operationName, $"Endpoint Parameter. Tenant ID '{tenantId}' base Uri '{baseUri ?? string.Empty}' requestPath '{requestPath ?? string.Empty}' Resource '{resource}' ");
            var requestUri = new Uri(Common.Utilities.Utilities.GetUri(baseUri), requestPath);
            var requestUriBuilder = new UriBuilder(requestUri)
            {
                Query = this.GenerateQuery(requestUri, queryParameters).ToString(),
            };

            try
            {
                this.EventSource.Debug(operationName, $"Calling Endpoint '{requestUriBuilder.Uri}'");

                using (var requestMessage = new HttpRequestMessage(method, requestUriBuilder.Uri))
                {
                    if (message != null)
                    {
                        this.EventSource.Debug(operationName, $"Outgoing Request Payload '{message.ToJson()}'");
                        requestMessage.Content = new StringContent(content: message.ToJson(), encoding: Encoding.UTF8, mediaType: "application/json");
                    }

                    var authenticationToken = await this.GetAuthenticationTokenFromAad(tenantId, resource)
                        .ConfigureAwait(continueOnCapturedContext: false);

                    requestMessage.Headers.Authorization = new AuthenticationHeaderValue(
                        authenticationToken.TokenType ?? ProviderConstants.Authorizations.BearerTokenType,
                        authenticationToken.AccessToken);

                    if (requestHeaders != null)
                    {
                        requestHeaders.CoalesceDictionary()
                            .ForEach(header => requestMessage.Headers.Add(header.Key, header.Value));
                        requestMessage.Headers.Add(name: "client-request-id",
                            value: RequestCorrelationContext.Current.CorrelationId);

                        this.EventSource.Debug(operationName, $"Request Header '{requestHeaders?.ToJson()}'");
                    }

                    Func<Task<HttpResponseMessage>> sendRequest = () =>
                        this.FrontdoorClient.CallFrontdoorService(requestMessage);

                    using (var response = await AsyncRetry.Retry(sendRequest,
                               FrontdoorEngine.PrivateLinkMaxRetries,
                               TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                               FrontdoorEngine.GraphApiRetryTimeout,
                               isRetryable: ex => ex.IsConnectivityException()
                                                  || (ex is ServerErrorResponseMessageException exception
                                                      && exception.HttpStatus.IsRetryableResponse()),
                               errorAction: (Exception exception) =>
                               {
                                   this.EventSource.Debug(operationName, $"Attempt to {method.Method} '{requestUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{Utilities.FlattenException(exception)}'.");
                               },
                               ex => (ex as ServiceThrottlingException)?.RetryAfter).ConfigureAwait(false))
                    {
                        this.EventSource.Debug(operationName, $"[Uri:{method.Method} {requestUri.AbsoluteUri}] [Response = {response?.StatusCode}] [ResponseHeaders: {response?.Headers}] [Response: {(response?.Content != null ? await response.Content.ReadAsStringAsync() : "None")}");

                        if (response?.StatusCode == HttpStatusCode.Created || response?.StatusCode == HttpStatusCode.Accepted || response?.StatusCode == HttpStatusCode.OK)
                        {
                            var stringResponse = await response.Content.ReadAsStringAsync().ConfigureAwait(continueOnCapturedContext: false);
                            if (String.IsNullOrWhiteSpace(stringResponse) || stringResponse == "{}")
                            {
                                return default(TMessage);
                            }
                            else
                            {
                                return stringResponse.TryFromJson<TMessage>();
                            }
                        }

                        if (response?.StatusCode == HttpStatusCode.Forbidden)
                        {
                            throw new AuthorizationOperationException(
                                httpStatus: HttpStatusCode.Forbidden,
                                errorCode: ErrorResponseCode.AuthorizationFailed.ToString(),
                                errorMessage: ErrorResponseMessages.UserUnauthorizedGeneralError.ToLocalizedMessage());
                        }

                        if ((response == null || response.StatusCode.IsSuccessfulRequest()) || response.StatusCode == HttpStatusCode.NotFound)
                        {
                            return default(TMessage);
                        }

                        var errorResponseBody = await response.Content.ReadAsStringAsync().ConfigureAwait(continueOnCapturedContext: false);

                        var errorResponseMessage = HandleEndpointErrorResponse(requestUri, errorResponseBody, response.StatusCode);

                        if (errorResponseMessage != null && !String.IsNullOrWhiteSpace(errorResponseMessage.Code))
                        {
                            this.EventSource.Debug(operationName, $"Rethrow server exception based on response - {errorResponseBody}");

                            throw new ServerErrorResponseMessageException(
                                response.StatusCode,
                                errorResponseMessage.Code ?? ErrorResponseCode.InternalServerError.ToString(),
                                errorResponseMessage.Message ?? errorResponseBody);
                        }

                        throw new ServerErrorResponseMessageException(
                           response.StatusCode,
                           ErrorResponseCode.InternalServerError.ToString(),
                           ErrorResponseMessages.RoleAssignmentInternalServerError.ToLocalizedMessage(response.StatusCode, errorResponseBody));
                    }
                }
            }
            catch (ServerErrorResponseMessageException)
            {
                throw;
            }
            catch (Exception exception)
            {
                this.EventSource.Error(
                    operationName,
                    $"Error: {method.Method} failed on URI : '{requestUri.AbsoluteUri}' in CustomerTenantId: {tenantId}. Error: {Utilities.FlattenException(exception)}");

                throw;
            }
        }

        #endregion

        #region Graph Endpoint

        /// <summary>
        /// Gets the graph directory object (if exists) for a given principal id.
        /// </summary>
        /// <param name="tenantId">The id for the tenant that the object belongs under.</param>
        /// <param name="principalId">The id for the graph resource.</param>
        public async Task<GraphPrincipalDetail> GetGraphDirectoryObjectFromPrincipalId(string tenantId, string principalId)
        {

            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            JObject graphApiResult = default(JObject);
            graphApiResult = await GetGraphPrincipalId(tenantId, principalId).ConfigureAwait(false);

            GraphPrincipalDetail graphPrincipalDetail = null;

            if (graphApiResult == default(JObject))
            {
                this.EventSource.Debug(operationName, $"unable to find Principal of Type User/Group. Searching in Applications.");

                //If the principalId is an appId, use the below API to resolve
                graphPrincipalDetail = await GetGraphApplicationId(tenantId, principalId).ConfigureAwait(false);
            }
            else
            {
                graphPrincipalDetail = this.GetGraphResourceDefinitionFromJObject(graphApiResult);
            }

            return graphPrincipalDetail;
        }

        /// <summary>
        /// Get Directory Object details for Users and Group Object Type
        /// </summary>
        /// <param name="tenantId">Home Tenant ID</param>
        /// <param name="principalId">Graph Object ID</param>
        /// <returns></returns>
        private async Task<JObject> GetGraphPrincipalId(string tenantId, string principalId)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            JObject graphApiResult = default(JObject);

            try
            {
                var requestUri = UriTemplateEngine.GetGraphDirectoryObjectsTemplate.BindByName(
                baseAddress: new Uri(Utilities.GraphApiEndpoint),
                parameters: new NameValueCollection
                {
                    { "api-version", Utilities.GraphApiVersion },
                    { "principalId", principalId }
                });

                this.EventSource.Debug(operationName, $"Attempt to GET '{requestUri.AbsoluteUri}'.");

                graphApiResult = await this.CallEndpoint<JObject>(
                    tenantId,
                    requestUri.AbsoluteUri,
                    string.Empty,
                    HttpMethod.Get,
                    default(JObject),
                    ProviderConstants.GraphApiEndpoint)
                    .ConfigureAwait(continueOnCapturedContext: false);
            }
            catch (Exception ex)
            {
                this.EventSource.Error(ex, operationName, ex.ToString());
                throw;
            }

            return graphApiResult;
        }

        /// <summary>
        /// Get Application ID Graph API details
        /// </summary>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="applicationId">Application ID</param>
        /// <returns></returns>
        public async Task<GraphPrincipalDetail> GetGraphApplicationId(string tenantId, string applicationId)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            GraphPrincipalDetail graphPrincipalDetail = null;

            try
            {
                var requestUri = Utilities.GetAppIdFilterQuery("servicePrincipals", "appId", applicationId);

                this.EventSource.Debug(operationName, $"Attempt to GET '{requestUri.AbsoluteUri}'.");

                var graphApiResult = await this.CallEndpoint<GraphCollectionResponse<GraphPrincipalDetail>>(
                    tenantId,
                    requestUri.AbsoluteUri,
                    string.Empty,
                    HttpMethod.Get,
                    null,
                    ProviderConstants.GraphApiEndpoint,
                    new InsensitiveDictionary<string> { { ProviderConstants.HeaderNameConsistencyLevel, ProviderConstants.HeaderValueEventual } })
                    .ConfigureAwait(continueOnCapturedContext: false);

                graphPrincipalDetail = graphApiResult?.Collection?.FirstOrDefault();
                if (null != graphPrincipalDetail)
                {
                    graphPrincipalDetail.PrincipalType = PrincipalType.App;
                }
                else
                {
                    throw new ErrorResponseMessageException(
                        httpStatus: HttpStatusCode.NotFound,
                        errorCode: ErrorResponseCode.ResourceNotFound,
                        errorMessage: ErrorResponseMessages.GetPrincipalDetailsFromGraphAPIFailed.ToLocalizedMessage(
                            applicationId));
                }
            }
            catch (Exception ex)
            {
                this.EventSource.Error(ex, operationName, ex.ToString());
                throw;
            }

            return graphPrincipalDetail;
        }

        #endregion

        #region Identity Refresh
        /// <summary>
        /// Gets the credentials for the identities from MIRP data plane.
        /// </summary>
        /// <param name="identityUrl">The URL of the identity.</param>
        /// <param name="credentialRequest">The Credential API parameters.</param>
        public async Task<IdentitiesCredential> GetIdentitiesCredentials(
            string identityUrl,
            CredentialRequest credentialRequest)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            try
            {
                var credentialRequestUri = this.CreateIdentitiesCredentialRequestUri(identityUrl);

                using (var credentialsResponse = await this
                    .CallIdentityProviderWithRetry(
                        credentialRequestUri,
                        credentialRequest)
                    .ConfigureAwait(continueOnCapturedContext: false))
                {
                    if (!credentialsResponse.StatusCode.IsSuccessfulRequest())
                    {
                        var errorResponse = await credentialsResponse.Content
                            .ReadAsStringAsync()
                            .ConfigureAwait(continueOnCapturedContext: false);

                        var expMessage = ErrorResponseMessages.FailedToGetIdentitiesCredentials.ToLocalizedMessage(
                            credentialsResponse.StatusCode,
                            errorResponse.ToJson());
                        throw new InvalidOperationException(expMessage);
                    }

                    return await credentialsResponse.Content
                        .ReadAsJsonAsync<IdentitiesCredential>(MediaTypeFormatter)
                        .ConfigureAwait(false);
                }
            }
            catch (Exception ex)
            {
                if (ex.IsFatal())
                {
                    throw;
                }

                this.EventSource.Error(operationName, "Failed to get resource identity credentials. Exception: {0}", ex);

                var expMessage = ErrorResponseMessages.IdentityProviderCommunicationErrorMessage.ToLocalizedMessage(
                        DateTime.UtcNow.ToSortableString(),
                        RequestCorrelationContext.Current.SubscriptionId,
                        RequestCorrelationContext.Current.CurrentActivityId,
                        RequestCorrelationContext.Current.CorrelationId);

                throw new AccessConnectorErrorResponse(
                    HttpStatusCode.InternalServerError,
                    ErrorResponseCode.InternalResourceIdentityError.ToString(),
                    expMessage);
            }
        }

        /// <summary>
        /// Calls the identity provider with retry.
        /// </summary>
        /// <param name="identityUri">The URI of the identity Credentials request.</param>
        /// <param name="credentialRequest">credentials request parameters</param>
        public Task<HttpResponseMessage> CallIdentityProviderWithRetry(
            Uri identityUri,
            CredentialRequest credentialRequest)
        {
            return AsyncRetry.Retry<HttpResponseMessage>(
                async () =>
                {
                    using (var requestMessage = new HttpRequestMessage(HttpMethod.Post, identityUri))
                    {
                        requestMessage.Content = credentialRequest != null ?
                            new ObjectContent<CredentialRequest>(credentialRequest, JsonExtensions.JsonMediaTypeFormatter)
                            : null;

                        var response = await this
                            .CallIdentityProvider(requestMessage)
                            .ConfigureAwait(false);

                        if (response != null && response.StatusCode.IsRetryableResponse())
                        {
                            response.Dispose();
                            throw new InvalidOperationException($"Received server error from identity service. Status code: '{response.StatusCode}'.");
                        }

                        return response;
                    }
                },
                FrontdoorEngine.IdentityServiceRetryCount,
                FrontdoorEngine.IdentityServiceRetryInterval);
        }

        /// <summary>
        /// Calls the identity provider.
        /// </summary>
        /// <param name="requestMessage">The request message.</param>
        /// <param name="authenticationUri">The authentication ur.</param>
        /// <param name="authenticationAudience">The authentication audience.</param>
        private async Task<HttpResponseMessage> CallIdentityProvider(HttpRequestMessage requestMessage, string authenticationUri = null, string authenticationAudience = null)
        {
            var targetAudience = FrontdoorEngine.ManagedIdentityDataPlaneAudience;
            using (var client = ServiceClientDataProvider.CreateServiceClient(requestMessage.RequestUri))
            {
                var httpBearerChallenge = await this
                    .GetAuthenticationChallengeHeaders(
                        requestMessage.RequestUri,
                        client)
                    .ConfigureAwait(false);

                var token = await this.GetAuthenticationTokenFromAad(
                        httpBearerChallenge.AuthorizationBaseUri,
                        httpBearerChallenge.Tenant,
                        targetAudience)
                    .ConfigureAwait(false);
                requestMessage.Headers.Authorization = new AuthenticationHeaderValue(ProviderConstants.Authorizations.BearerTokenType, token.AccessToken);

                return await ProvidersLog.Current
                    .TraceHttpOutgoingRequest(
                        requestMessage.GetRESTfulRequestOperationName(),
                        requestMessage,
                        () => client.SendAsync(requestMessage, HttpCompletionOption.ResponseContentRead))
                    .ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Gets the authentication challenge headers from MSI RP.
        /// </summary>
        /// <param name="identityUri">The managed service identity Uri.</param>
        /// <param name="serviceClient">The client to make the request.</param>
        private async Task<HttpBearerChallenge> GetAuthenticationChallengeHeaders(Uri identityUri, HttpClient serviceClient)
        {
            using (var requestMessage = new HttpRequestMessage(HttpMethod.Get, identityUri))
            {
                using (var response = await ProvidersLog.Current
                    .TraceHttpOutgoingRequest(
                        request: requestMessage,
                        operationName: requestMessage.GetRESTfulRequestOperationName(),
                        action: () => serviceClient.SendAsync(requestMessage))
                    .ConfigureAwait(false))
                {
                    if (response.StatusCode == HttpStatusCode.Unauthorized)
                    {
                        // Sample: authorization="https://login.windows-ppe.net/5D929AE3-B37C-46AA-A3C8-C1558902F101"
                        // For security reasons, resource is no longer included as a part of the www-authenticate challenge header returned
                        // in the 401 Unauthorized response.
                        var bearerChallengeHeader = response.Headers.WwwAuthenticate
                            .FirstOrDefault(header => header.Scheme.EqualsInsensitively(ProviderConstants.Authorizations.BearerTokenType));

                        if (bearerChallengeHeader != null &&
                            HttpBearerChallenge.TryCreateHttpBearerChallenge(bearerChallengeHeader.Parameter, out var httpBearerChallenge))
                        {
                            return httpBearerChallenge;
                        }
                    }

                    throw new InvalidOperationException(message:
                        $"Received an invalid response from identity service on bearer challenge. Status code: '{response.StatusCode}'.");
                }
            }
        }

        /// <summary>
        /// Appends the API version query parameter to the identity URL
        /// </summary>
        /// <param name="identityUrl">The identity URL</param>
        private Uri CreateIdentitiesCredentialRequestUri(string identityUrl)
        {
            var operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            if (string.IsNullOrWhiteSpace(identityUrl))
            {
                this.EventSource.Error(
                    operationName,
                    $"Invalid resource identity url: {identityUrl}");

                throw new ArgumentException(message: $"Invalid resource identity url {identityUrl}");
            }

            var identityUri = new UriBuilder(identityUrl);

            if (identityUri.Uri.ParseQueryString().Get("api-version") == null)
            {
                var apiVersionQueryString = "api-version=" + FrontdoorEngine.ManagedIdentityDataPlaneApiVersion;

                if (identityUri.Query.Length > 1)
                {
                    identityUri.Query = identityUri.Query.Substring(1) + "&" + apiVersionQueryString;
                }
                else
                {
                    identityUri.Query = apiVersionQueryString;
                }
            }

            return identityUri.Uri;
        }

        #endregion

        #region dbPrivateEndpoint
        /// <summary>
        /// Calls db privateLinkResources endpoint to retrieve list of supported group Id with Required members for a given workspace
        /// </summary>
        /// <param name="workspaceId">Workspace Azure Resource ID</param>
        /// <param name="apiVersion">DB API Version</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <returns>Returns dbPrivateLinkResources</returns>
        public async Task<List<PrivateLinkResource>> GetDbPrivateLinkResources(string workspaceId,
            string apiVersion,
            string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var databricksPrivateLinkResourceUri = UriTemplateEngine.DbPrivateLinkResourceUri(
                this.FrontdoorEndpointUri,
                workspaceId,
                apiVersion);

            var privateLinkResources = new List<PrivateLinkResource>();

            this.EventSource.Debug(operationName,
                $"Calling dbPrivateLinkResource endpoint : {databricksPrivateLinkResourceUri}'");

            try
            {
                // Call Databricks Endpoint
                var databricksPrivateLinkResourceResponseList = await AsyncRetry.Retry(
                        () => this.GetResourcesFromFrontdoor<DbPrivateLinkResourceList>(
                            RequestCorrelationContext.Current.GetHomeTenantId(),
                            databricksPrivateLinkResourceUri,
                            resourceProviderNamespace),
                        FrontdoorEngine.DBWorkspaceNotificationRetryCount,
                        TimeSpan.FromSeconds(FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs),
                        isRetryable: ex =>
                            ex.IsConnectivityException() ||
                            (ex is ServerErrorResponseMessageException exception &&
                             exception.HttpStatus.IsRetryableResponse()),
                        errorAction: (Exception exception) =>
                        {
                            this.EventSource.Debug(
                                operationName,
                                $"Attempt to DbPrivateLinkResourceUri failed. Framework will retry in {FrontdoorEngine.DBWorkspaceNotificationRetryIntervalSecs} seconds.Exception details: '{Utilities.FlattenException(exception)}'");
                        })
                    .ConfigureAwait(false);

                if (databricksPrivateLinkResourceResponseList?.Value?.Count > 0)
                {
                    this.EventSource.Debug(operationName,
                        $"dbPrivateLinkResource response : {databricksPrivateLinkResourceResponseList.ToJson()}'");

                    privateLinkResources = databricksPrivateLinkResourceResponseList.Value.Select(x =>
                            new PrivateLinkResource(workspaceId,
                                x.Properties.GroupId,
                                x.Properties.RequiredMembers.ToArray(),
                                x.Properties.RequiredZoneNames.ToArray()))
                        .ToList();
                }
            }
            catch (Exception exception)
            {
                this.EventSource.Error(operationName,
                    "Unhandled exception while calling DB endpoint - {0}.",
                    databricksPrivateLinkResourceUri,
                    exception);
            }

            return privateLinkResources;
        }

        /// <summary>
        /// Deletes a Private endpoint connection.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="privateEndpointConnectionId">The private endpoint connection Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public Task<Uri> DeletePrivateEndpointConnection(
            string authenticationTenantId,
            string privateEndpointConnectionId,
            string resourceProviderNamespace)
        {
            var resourceUri = UriTemplateEngine.GetPrivateEndpointConnectionRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                privateEndpointConnectionId: privateEndpointConnectionId,
                apiVersion: ProviderConstants.ApiVersion20180401);

            return this.ExecuteDeleteResourceOperation<JObject>(
                authenticationTenantId: authenticationTenantId,
                httpMethod: HttpMethod.Delete,
                requestUri: resourceUri,
                resourceProviderNamespace: resourceProviderNamespace,
                requestBody: null);
        }

        /// <summary>
        /// Deletes a list of resources specified in the request body
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token</param>
        /// <param name="requestBody">Request body contains the resources to be deleted</param>
        /// <returns></returns>
        public Task<Uri> DeleteResourcesInBulk(
            string authenticationTenantId,
            JObject requestBody,
            string resourceProviderNamespace)
        {
            var requestUri = UriTemplateEngine.GetBulkDeleteUri(
                baseUri: this.FrontdoorEndpointUri,
                apiVersion: ProviderConstants.BulkDeleteApiVersion);

            return this.ExecuteDeleteResourceOperation<JObject>(
                authenticationTenantId: authenticationTenantId,
                httpMethod: HttpMethod.Post,
                requestUri: requestUri,
                requestBody: requestBody,
                resourceProviderNamespace: resourceProviderNamespace);
        }

        #endregion

        #region Access Connector

        /// <summary>
        /// Delete an Access Connector Resource
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="accessConnectorId">The access connector resource id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public Task<Uri> DeleteAccessConnector(
            string authenticationTenantId,
            string accessConnectorId,
            string resourceProviderNamespace)
        {
            var resourceUri = UriTemplateEngine.GetAccessConnectorRequestUri(
                endpoint: this.FrontdoorEndpointUri,
                accessConnectorId: accessConnectorId,
                apiVersion: ProviderConstants.Databricks.AccessConnector20240501ApiVersion);

            return this.ExecuteDeleteResourceOperation<JObject>(
                authenticationTenantId: authenticationTenantId,
                httpMethod: HttpMethod.Delete,
                requestUri: resourceUri,
                resourceProviderNamespace: resourceProviderNamespace,
                requestBody: null);
        }

        #endregion

        #region Security Rule
        /// <summary>
        /// Gets a security rule.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="resourceGroupUri">The resource group name.</param>
        /// <param name="securityGroupName">The NSG name.</param>
        /// <param name="securityRuleName">The security rule name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task<SecurityRuleDefinition> GetNsgSecurityRule(
            string authenticationTenantId,
            string resourceGroupUri,
            string securityGroupName,
            string securityRuleName,
            string resourceProviderNamespace)
        {
            var securityRuleUri = UriTemplateEngine.WorkerSecurityRuleUri(
                endpoint: this.FrontdoorEndpointUri,
                resourceGroupUri: resourceGroupUri,
                securityGroupName: securityGroupName,
                securityRuleName: securityRuleName,
                apiVersion: FrontdoorEngine.SecurityRuleApiVersion);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Calling frontdoor: GET '{securityRuleUri.AbsoluteUri}'...");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            return await AsyncRetry.Retry(
                () => this.GetResourcesFromFrontdoor<SecurityRuleDefinition>(
                    authenticationTenantId,
                    securityRuleUri,
                    resourceProviderNamespace),
                ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                isRetryable: ex => ex.IsConnectivityException() ||
                                   (ex is ServerErrorResponseMessageException exception &&
                                    exception.HttpStatus.IsRetryableResponse()),
                errorAction: (Exception exception) =>
                {
                    this.EventSource.Debug(
                        this.GetOperationName(Utilities.GetAsyncMethodName()),
                        $"Attempt to GET '{securityRuleUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                })
            .ConfigureAwait(false);
        }

        /// <summary>
        /// Create or update a security rule.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="resourceGroupUri">The resource group name.</param>
        /// <param name="securityGroupName">The NSG name.</param>
        /// <param name="securityRuleName">The security rule name.</param>
        /// <param name="securityRuleDefinition">The security rule definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        public async Task CreateOrUpdateNsgSecurityRule(
            string authenticationTenantId,
            string resourceGroupUri,
            string securityGroupName,
            string securityRuleName,
            SecurityRuleDefinition securityRuleDefinition,
            string resourceProviderNamespace)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());

            var securityRuleUri = UriTemplateEngine.WorkerSecurityRuleUri(
                endpoint: this.FrontdoorEndpointUri,
                resourceGroupUri: resourceGroupUri,
                securityGroupName: securityGroupName,
                securityRuleName: securityRuleName,
                apiVersion: FrontdoorEngine.SecurityRuleApiVersion);

            this.EventSource.Debug(
                this.GetOperationName(Utilities.GetAsyncMethodName()),
                $"Calling frontdoor: PUT '{securityRuleUri.AbsoluteUri}', request body: {JsonConvert.SerializeObject(securityRuleDefinition)}");

            var authenticationToken = await this
                .GetAuthenticationTokenFromCache(
                    tenantId: authenticationTenantId)
                .ConfigureAwait(continueOnCapturedContext: false);

            await AsyncRetry.Retry(
                () => CallFrontdoorServiceWithException(
                        method: HttpMethod.Put,
                        requestUri: securityRuleUri,
                        accessToken: authenticationToken.AccessToken,
                        userAgent: resourceProviderNamespace,
                        requestBody: securityRuleDefinition),
                ProviderConstants.MicrosoftNetwork.MaxRetryCount,
                TimeSpan.FromSeconds(ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds),
                isRetryable: ex =>
                    ex.IsConnectivityException() ||
                    (ex is ServerErrorResponseMessageException exception &&
                    exception.HttpStatus.IsRetryableResponse()),
                errorAction: (Exception exception) =>
                {
                    this.EventSource.Debug(operationName,
                        $"Attempt to PUT '{securityRuleUri.AbsoluteUri}' failed. Framework will retry in {ProviderConstants.MicrosoftNetwork.RetryIntervalSeconds} seconds.Exception details: '{exception}'.");
                })
            .ConfigureAwait(false);
        }

        /// <summary>
        /// Call front door service and throw exception on failed HTTP response
        /// This works better with AsyncRetry as AsyncRetry relies on the exception on failure
        /// </summary>
        /// <typeparam name="T">The type of request body.</typeparam>
        /// <param name="method">The http method.</param>
        /// <param name="requestUri">The request uri.</param>
        /// <param name="accessToken">The access token.</param>
        /// <param name="requestBody">The request body.</param>
        /// <param name="userAgent">The user agent.</param>
        /// <param name="additionalHeaders">Additional headers to add to the request.</param>
        private async Task<HttpResponseMessage> CallFrontdoorServiceWithException<T>(
            HttpMethod method,
            Uri requestUri,
            string accessToken,
            T requestBody,
            string userAgent = null,
            KeyValuePair<string, string>[] additionalHeaders = null)
        {
            string operationName = this.GetOperationName(Utilities.GetAsyncMethodName());
            this.EventSource.Debug(
                operationName,
                $"Calling Frontdoor service at {requestUri}");

            using (var response = await this.FrontdoorClient
                    .CallFrontdoorService(
                        method,
                        requestUri,
                        accessToken,
                        requestBody,
                        userAgent,
                        additionalHeaders))
            {
                if (response == null || response.StatusCode == HttpStatusCode.NotFound)
                {
                    this.EventSource.Debug(
                        operationName,
                        $"Frontdoor didn't return response, ResponseCode: {response.StatusCode}");

                    return default;
                }

                if (!response.StatusCode.IsSuccessfulRequest())
                {
                    var errorResponseMessage = await this
                        .GetErrorResponseMessage(response)
                        .ConfigureAwait(false);

                    this.EventSource.Debug(
                        operationName,
                        $"Found error - ResponseCode: {response.StatusCode}");

                    if (errorResponseMessage != null)
                    {
                        this.EventSource.Debug(
                            operationName,
                            $"ErrorCode: {errorResponseMessage.Error.Code} | ErrorMessage: {errorResponseMessage.Error.Message}");

                        throw new ServerErrorResponseMessageException(
                            response.StatusCode,
                            errorResponseMessage.Error.Code,
                            errorResponseMessage.Error.Message);
                    }

                    throw new ServerErrorResponseMessageException(
                        response.StatusCode,
                        ErrorResponseCode.InternalServerError.ToString(),
                        ErrorResponseMessages.InternalServerError.ToLocalizedMessage(RequestCorrelationContext.Current.CorrelationId));
                }

                return response;
            }
        }
        #endregion
    }
}