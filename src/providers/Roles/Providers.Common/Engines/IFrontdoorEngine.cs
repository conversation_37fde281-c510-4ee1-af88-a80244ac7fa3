﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines
{
    using System;
    using System.Collections.Generic;
    using System.Collections.Specialized;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.Identity.Client;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.Deployments;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.SecurityRule;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Newtonsoft.Json.Linq;
    using HttpResponseHeaders = System.Net.Http.Headers.HttpResponseHeaders;

    /// <summary>
    /// Interface that defines the behavior of the Front door engine.
    /// </summary>
    public interface IFrontdoorEngine
    {
        /// <summary>
        /// Gets the storage update API version.
        /// </summary>
        /// <value>
        /// Gets The storage update API version.
        /// </value>
        string StorageApiVersion { get; }

        /// <summary>
        /// Gets the front door endpoint URI.
        /// </summary>
        Uri FrontdoorEndpointUri { get; }

        /// <summary>
        /// Gets the Workspace Initialize Retry Interval in seconds
        /// </summary>
        int WorkspaceInitializeRetryCount { get; }

        /// <summary>
        /// Gets the Workspace Initialize Retry Interval in seconds
        /// </summary>
        int WorkspaceInitializeRetryInterval { get; }

        int AccountApiPollingRetryCount { get; }

        /// <summary>
        /// Gets the Workspace Initialize Retry Interval in seconds
        /// </summary>
        int AccountApiPollingRetryInterval { get; }

        /// <summary>
        /// Gets the cache providers container
        /// </summary>
        ICacheProvidersContainer CacheProvidersContainer { get; }

        /// <summary>
        /// Gets a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="expand">The expand query to get more properties</param>
        Task<ResourceGroupDefinition> GetResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace,
            string expand = null);

        /// <summary>
        /// Creates a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceGroupDefinition">The resource group definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            ResourceGroupDefinition resourceGroupDefinition,
            string resourceProviderNamespace);

        /// <summary>
        /// Updates a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant ID for authentication.</param>
        /// <param name="subscriptionId">The subscription ID where the resource group belongs to.</param>
        /// <param name="resourceGroupName">The name of the resource group.</param>
        /// <param name="resourceGroupDefinition">The definition for the resource group</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <returns>The <c>Awaitable</c>.</returns>
        Task UpdateResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            ResourceGroupDefinition resourceGroupDefinition,
            string resourceProviderNamespace);

        /// <summary>
        /// Updates the tags for the managed resource.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant ID for authentication.</param>
        /// <param name="subscriptionId">The subscription ID</param>
        /// <param name="resourceProviderNamespace">The namespace of the resource provider.</param>
        /// <param name="resource">The name of the resource.</param>
        /// <param name="tags">The list of tags to be updated.</param>
        /// <returns>The <c>Awaitable</c>.</returns>
        Task UpdateManagedResourceTags(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace,
            ARMResourceDefinition resource,
            InsensitiveDictionary<string> tags);

        /// <summary>
        /// Deletes a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<Uri> DeleteResourceGroup(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets all resources in a resource group.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant ID for authentication/</param>
        /// <param name="subscriptionId">The subscription id for the resource group</param>
        /// <param name="resourceGroupName">The name of the resource group.</param>
        /// <param name="resourceProviderNamespace">The namespace of the resource provider whose resources are to be retrieved</param>
        /// <param name="filter"> The OData filter condition</param>
        /// <param name="top">Number of resources to be returned or null to return all of them</param>
        /// <returns>The list of resource definitions of the resources in the resource group.</returns>
        Task<ARMResourceDefinition[]> GetResourceGroupResources(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string resourceProviderNamespace,
            string filter,
            string top);

        /// <summary>
        /// Creates a role assignment if one does not already exist for a given principal/role definition pair.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope on which role assignment is to be created.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="roleAssignmentDefinition">The role assignment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateRoleAssignmentIfNotExists(
            string authenticationTenantId,
            string scope,
            string principalId,
            string roleDefinitionId,
            RoleAssignmentDefinition roleAssignmentDefinition,
            string resourceProviderNamespace);

        /// <summary>
        /// Deletes role assignments if exist for a given principal/role definition pair.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope on which role assignment is to be created.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task DeleteRoleAssignmentIfExists(
            string authenticationTenantId,
            string scope,
            string principalId,
            string roleDefinitionId,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets the role assignments for a given principal.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="roleDefinitionId">The role definition id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="additionalHeaders">Optional. Additional headers.</param> 
        Task<RoleAssignmentDefinition[]> GetRoleAssignments(
            string authenticationTenantId,
            string scope,
            string principalId,
            string roleDefinitionId,
            string resourceProviderNamespace,
            KeyValuePair<string, string>[] additionalHeaders = null);

        /// <summary>
        /// Registers a subscription with a managed tenant.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="publisherTenantId">The publisher tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="resourceId">The resource Id to map with managed by tenant registration.</param>
        Task RegisterSubscriptionWithManagedTenant(
            string authenticationTenantId,
            string subscriptionId,
            string publisherTenantId,
            string resourceProviderNamespace,
            string apiVersion = null,
            string resourceId = null);

        /// <summary>
        /// Get Databricks Account API endpoint and audience, based on feature flag
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="subscriptionId"></param>
        /// <returns></returns>
        Task<(Uri, string,bool)> GetDatabricksAccountApiEnpointAndAudience(string tenantId, string subscriptionId);

        /// <summary>
        /// Unregisters a subscription with a managed tenant.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="publisherTenantId">The publisher tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="apiVersion">The API version.</param>
        /// <param name="resourceId">The resource Id to map with managed by tenant registration.</param>
        Task UnregisterSubscriptionWithManagedTenant(
            string authenticationTenantId,
            string subscriptionId,
            string publisherTenantId,
            string resourceProviderNamespace,
            string apiVersion = null,
            string resourceId = null);

        /// <summary>
        /// Creates an authorization resource.
        /// </summary>
        /// <typeparam name="T">The type of authorization request body.</typeparam>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="authorizationResourceDefinition">The resource definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateAuthorizationResource<T>(
            string authenticationTenantId,
            Uri requestUri,
            T authorizationResourceDefinition,
            string resourceProviderNamespace);

        /// <summary>
        /// Deletes an authorization resource.
        /// </summary>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task DeleteAuthorizationResource(
            string authenticationTenantId,
            Uri requestUri,
            string resourceProviderNamespace);

        /// <summary>
        /// Creates an authorization resource.
        /// </summary>
        /// <typeparam name="T">The type of authorization request body.</typeparam>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="additionalHeaders">Optional. Additional headers.</param>
        Task<T> GetAuthorizationResources<T>(
            string authenticationTenantId,
            Uri requestUri,
            string resourceProviderNamespace,
            KeyValuePair<string, string>[] additionalHeaders = null);

        /// <summary>
        /// Gets the system deny assignments for a given principal.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="principalId">The principal id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<DenyAssignmentDefinition[]> GetSystemDenyAssignments(
            string authenticationTenantId,
            string scope,
            string principalId,
            string resourceProviderNamespace);

        /// <summary>
        /// Check if the name of the dbfs account is available to use or not.
        /// </summary>
        /// <param name="authenticationTenantId">AAD tenantId to get token</param>
        /// <param name="subscriptionId">customer subscription Id</param>
        /// <param name="parameters">workspace entity parameters</param>
        /// <param name="resourceProviderNamespace">resource provider namespace</param>
        /// <returns></returns>
        Task CheckDbfsAccountNameAvailability(
                    string authenticationTenantId,
                    string subscriptionId,
                    InsensitiveDictionary<JToken> parameters,
                    string resourceProviderNamespace);

        /// <summary>
        /// Gets containers present in dbfs account except UC Container.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="dbfsResourceId">Dbfs Account Resource Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<IEnumerable<StorageContainer>> GetDbfsContainers(
            string authenticationTenantId,
            string dbfsResourceId,
            string resourceProviderNamespace);

        /// <summary>
        /// Deletes a container in storage account.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="dbfsResourceId">Dbfs Account Resource Id.</param>
        /// <param name="containerName">Dbfs Container Name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<Uri> DeleteStorageAccountContainer(
            string authenticationTenantId,
            string dbfsResourceId,
            string containerName,
            string resourceProviderNamespace);

        /// <summary>
        /// Creates a deny assignment if it does not exist or updated a deny assignment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope on which deny assignment is to be created.</param>
        /// <param name="principalId">The principal Id.</param>
        /// <param name="denyAssignmentDefinition">The deny assignment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateOrUpdateDenyAssignments(
            string authenticationTenantId,
            string scope,
            string principalId,
            DenyAssignmentDefinition denyAssignmentDefinition,
            string resourceProviderNamespace);

        /// <summary>
        /// Creates a deny assignment if it does not exist or updated a deny assignment with the resource id.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="denyAssignmentResourceId">The deny assignment resource id.</param>
        /// <param name="denyAssignmentDefinition">The deny assignment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateOrUpdateDenyAssignmentsWithResourceId(
            string authenticationTenantId,
            string scope,
            string denyAssignmentResourceId,
            DenyAssignmentDefinition denyAssignmentDefinition,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets the system deny assignment resource id.
        /// </summary>
        /// <param name="systemDenyAssignmentDefinition">The system deny assignment definition.</param>
        /// <param name="scope">The scope.</param>
        string GetSystemDenyAssignmentResourceId(DenyAssignmentDefinition systemDenyAssignmentDefinition, string scope);

        /// <summary>
        /// Delete the system deny assignment resource id.
        /// </summary>
        /// <param name="authenticationTenantId">The customer home tenant Id used to retrieve access token.</param>
        /// <param name="scope">The scope.</param>
        /// <param name="principalId">The principal id.In this case EveryonePrincipalId</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param> 
        Task DeleteSystemDenyAssignment(
            string authenticationTenantId,
            string scope,
            string principalId,
            string resourceProviderNamespace);

        /// <summary>
        /// Creates a template deployment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="deploymentName">The deployment name.</param>
        /// <param name="deploymentDefinition">The deployment definition.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="forceTokenRefresh">Optional. Indicates whether to force refresh the token.</param>
        Task<DeploymentResponse> CreateDeployment(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string deploymentName,
            DeploymentDefinition deploymentDefinition,
            string resourceProviderNamespace,
            bool forceTokenRefresh = false);

        /// <summary>
        /// Gets a template deployment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="deploymentName">The deployment name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<DeploymentDefinition> GetDeployment(
            string authenticationTenantId,
            string subscriptionId,
            string resourceGroupName,
            string deploymentName,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets status of a deployment.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="forceTokenRefresh">Optional. Indicates whether to force refresh the token.</param>
        Task<AsyncOperationResult> GetDeploymentOperationStatus(
            string authenticationTenantId,
            Uri operationTrackingUri,
            string resourceProviderNamespace,
            bool forceTokenRefresh = false);

        /// <summary>
        /// Gets status of a resource group deletion.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<AsyncOperationResult> GetResourceGroupDeletionStatus(
            string authenticationTenantId,
            Uri operationTrackingUri,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets status of a bulk delete operation.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<BulkDeleteOperationResult> GetBulkDeleteOperationStatus(
            string authenticationTenantId,
            Uri operationTrackingUri,
            string resourceProviderNamespace);

        /// <summary>
        /// Registers subscription for a resource provider.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="authenticationResourceProviderNamespace">The authentication resource provider namespace used to get the token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="registrationResourceProvidersNamespace">Collection of resource provider namespace to register the subscription for.</param>
        /// <param name="signedOboToken">Signed Obo Token</param>
        Task RegisterSubscriptionForResourceProviders(
            string authenticationTenantId,
            string authenticationResourceProviderNamespace,
            string subscriptionId,
            List<string> registrationResourceProvidersNamespace,
            string signedOboToken);

        /// <summary>
        /// Check if the subscription is registered for a resource provider
        /// </summary>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="registrationResourceProviderNamespace">The authentication resource provider namespace used to get the token.</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        Task<bool> IsResourceProviderRegistered(
            string subscriptionId,
            string registrationResourceProviderNamespace,
            string authenticationTenantId
        );

        /// <summary>
        /// Calls front door.
        /// </summary>
        /// <param name="httpRequest">The http request.</param>
        /// <param name="requestUri">The request uri.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="requestBody">The request body.</param>
        Task<HttpResponseMessage> CallFrontdoor(
           HttpRequestMessage httpRequest,
           Uri requestUri,
           string resourceProviderNamespace,
           string tenantId,
           JToken requestBody);

        /// <summary>
        /// Gets the authentication token.
        /// </summary>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="forceRefresh">Optional. Indicates whether to force refresh the token.</param>
        Task<AuthenticationResult> GetAuthenticationTokenFromCache(string tenantId, string audience = null, bool forceRefresh = false);

        /// <summary>
        /// Refreshes the authentication token in the cache.
        /// </summary>
        /// <param name="tenantId">The tenant Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task RefreshAuthenticationToken(string tenantId, string resourceProviderNamespace);

        /// <summary>
        /// Prepare or un-prepare subnet with network intent policy.
        /// </summary>
        /// <param name="virtualNetworkInjectionOperation">The network intent policy operation on subnet</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subnetName">The subnet name.</param>
        /// <param name="requestUri">The request <c>uri</c>.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="virtualNetworkInjectionOperationRequest">The Virtual Network Injection Operation Request.</param>
        Task<HttpResponseHeaders> InitializeVirtualNetworkInjectionOperation(
            VirtualNetworkInjectionOperation virtualNetworkInjectionOperation,
            string authenticationTenantId,
            string subnetName,
            Uri requestUri,
            string resourceProviderNamespace,
            VirtualNetworkInjectionOperationRequest virtualNetworkInjectionOperationRequest);

        /// <summary>
        /// Gets status of a prepare or un-prepare.
        /// </summary>
        /// <param name="virtualNetworkInjectionOperation">The virtual network injection operation on subnet</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subnetName">The subnet name.</param>
        /// <param name="operationTrackingUri">The operation tracking URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<AsyncOperationResult> GetVirtualNetworkInjectionOperationStatus(
            VirtualNetworkInjectionOperation virtualNetworkInjectionOperation,
            string authenticationTenantId,
            string subnetName,
            Uri operationTrackingUri,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets the virtual network with subnets.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="vnetId">The virtual network id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<VirtualArmResource> GetVirtualNetwork(
            string authenticationTenantId,
            string vnetId,
            string resourceProviderNamespace);

        /// <summary>
        /// Updates the service endpoints of subnet
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="requestBody">The subnet request body</param>
        Task PutServiceEndpointsOnSubnet(
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            SubnetDefinition requestBody);

        /// <summary>
        /// Gets the AML Workspace.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="amlWorkspaceId">The AML Workspace ID.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<JObject> GetAmlWorkspace(
            string authenticationTenantId,
            string amlWorkspaceId,
            string resourceProviderNamespace);

        /// <summary>
        /// Updates the encryption settings on storage with BYOK asynchronously.
        /// </summary>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="managedResourceGroupName">Name of the managed resource group.</param>
        /// <param name="storageAccountName">Name of the storage account.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="storageUpdateRequest">The storage update request.</param>
        Task<HttpResponseMessage> UpdateStorageEncryptionWithByok(
            string subscriptionId,
            string managedResourceGroupName,
            string storageAccountName,
            string authenticationTenantId,
            string resourceProviderNamespace,
            StorageDefinition storageUpdateRequest);

        /// <summary>
        /// Patches the storage resource.
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="request">The request.</param>
        Task<HttpResponseMessage> PatchStorageResource(
            Uri requestUri,
            string resourceProviderNamespace,
            string authenticationTenantId,
            StorageDefinition request);

        /// <summary>
        /// Get the operation status for disk encryption set operation. 
        /// </summary>
        /// <param name="diskEncryptionSetOperation">The disk encryption set operation being performed on MRG.</param>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="diskEncryptionSetId">The disk encryption set ID.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="asyncTrackingUri">The async tracking URI</param>
        Task<DiskEncryptionSetOperationResponse> GetDiskEncryptionSetOperationStatus(
            DiskEncryptionSetOperation diskEncryptionSetOperation,
            string authenticationTenantId,
            string diskEncryptionSetId,
            string resourceProviderNamespace,
            Uri asyncTrackingUri);

        /// <summary>
        /// Creates a disk encryption set in the managed resource group
        /// </summary>
        /// <param name="managedResourceGroupId">Name of the managed resource group.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="subscriptionId">The subscription ID.</param>
        /// <param name="diskEncryptionSetCreateRequest">The disk encryption set create request.</param>
        Task<HttpResponseMessage> CreateManagedDiskEncryptionSetWithCmk(
            string managedResourceGroupId,
            string authenticationTenantId,
            string resourceProviderNamespace,
            string subscriptionId,
            DiskEncryptionSetDefinition diskEncryptionSetCreateRequest);

        /// <summary>
        /// Gets the disk encryption set in the MRG.
        /// </summary>
        /// <param name="managedResourceGroupId">Name of the managed resource group.</param>
        /// <param name="authenticationTenantId">The authentication tenant identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <param name="subscriptionId">The subscription ID.</param>
        Task<DiskEncryptionSetOperationResponse> GetManagedDiskEncryptionSet(
            string managedResourceGroupId,
            string authenticationTenantId,
            string resourceProviderNamespace,
            string subscriptionId);


        /// <summary>
        /// Gets feature registration by name.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="featureName">The feature name</param>
        Task<FeatureDefinition> GetFeatureRegistrationByName(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace,
            string featureName);

        /// <summary>
        /// Gets resource provider feature registrations for a subscription.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<ResponseWithContinuation<FeatureDefinition[]>> GetProviderFeatureRegistrations(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets list of registered features in given subscription.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription identifier.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<List<string>> GetRegisteredFeaturesInSubscription(
            string authenticationTenantId,
            string subscriptionId,
            string resourceProviderNamespace);

        /// <summary>
        /// Gets the dbWorkspace object from DbBackend Service
        /// </summary>
        /// <param name="resourceId">Workspace resourceId</param>
        /// <param name="apiVersion">DbBackend service API version</param>
        /// <param name="resourceProviderNamespace">Databricks RP namespace</param>
        /// <param name="retryCount">No of times get request to be retried</param>
        /// <param name="retryInterval">Amount of time to be waited between retries</param>
        /// <returns></returns>
        Task<T> GetDbWorkspaceDetailsAsync<T>(
            Uri requestUri,
            string apiVersion,
            string resourceProviderNamespace,
            string audience,
            int retryCount,
            int retryInterval);

        /// <summary>
        /// Gets resource(s) from ARM Front door.
        /// </summary>
        /// <typeparam name="T">The type of authorization request body.</typeparam>
        /// <param name="authenticationTenantId">The authentication tenant Id.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<T> GetResourcesFromFrontdoor<T>(
            string authenticationTenantId,
            Uri requestUri,
            string resourceProviderNamespace,
            string audience = null);

        /// <summary>
        /// Call Azure Resource Manager for any operation
        /// </summary>
        /// <typeparam name="T">The type of authorization response body.</typeparam>
        /// <param name="httpMethod">The HTTP Method</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders">Optional. Request headers.</param>
        /// <returns>The response of the operation or throws exception</returns>
        Task<T> CallFrontdoor<T>(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            T requestBody,
            string audience = null,
            KeyValuePair<string, string>[] additionalHeaders = null);

        /// <summary>
        /// Call Databricks Credential Manager for any operation
        /// </summary>
        /// <typeparam name="T">The type of authorization response body.</typeparam>
        /// <param name="httpMethod">The HTTP Method</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="audience">AAD Audience for request.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders">Additional headers to be passed to the request</param>
        /// <returns>The response of the operation or throws exception</returns>
        Task CallDatabricksAsyncAccountApi<T>(
            HttpMethod httpMethod,
            Uri requestUri,
            string tenantId,
            string audience,
            T requestBody,
            InsensitiveDictionary<string> additionalHeaders = null);

        /// <summary>
        /// Calls the identity provider with retry.
        /// </summary>
        /// <param name="identityUri">The URI of the identity Credentials request..</param>
        /// <param name="credentialRequest">credentials request parameters.</param>
        Task<HttpResponseMessage> CallIdentityProviderWithRetry(
            Uri identityUri,
            CredentialRequest credentialRequest);

        /// <summary>
        /// Call Azure Resource Manager for PUT azure asynchronous operation
        /// </summary>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="requestBody">The request body</param>
        Task<AzureAyncResponse> PutPrivateLinkServiceProxy(
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            JToken requestBody);

        /// <summary>
        /// Get Resource Home Tenant ID esp. scenario manual private link approval for cross tenant resources.
        /// </summary>
        /// <param name="requestUri"></param>
        /// <param name="resourceProviderNamespace"></param>
        /// <param name="defaultTenantId"></param>
        /// <returns></returns>
        Task<string> GetTenantIdAsync(
            Uri requestUri,
            string resourceProviderNamespace,
            string defaultTenantId = null);

        /// <summary>
        /// Get Resource Home Tenant ID.
        /// </summary>
        /// <param name="requestUri"></param>
        /// <returns>Customer tenant id</returns>
        Task<string> GetCustomerTenantIdAsync(Uri requestUri);


        /// <summary>
        /// DB workspace Notifications with feature flag
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders"></param>
        /// <returns></returns>
        Task DBWorkspaceNotificationWrapper(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            string audience,
            bool useArm = true,
            KeyValuePair<string, string>[] additionalHeaders = null);

        /// <summary>
        /// DB workspace Update Notifications with feature flag and polling response
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="audience">The audience for Account API</param>
        /// <param name="useArm">Whether to use ARM or Account API</param>
        /// <param name="baseUri">The base URI for Account API</param>
        /// <param name="additionalHeaders">Additional headers</param>
        /// <returns>Returns workspace update response with polling information</returns>
        Task<WorkspaceUpdateResponse> DBWorkspaceUpdateNotificationWrapper(
            HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            string audience,
            bool useArm,
            Uri baseUri,
            KeyValuePair<string, string>[] additionalHeaders = null);

        /// <summary>
        /// To call Azure Resource Manager for DB workspace Notifications
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders">Optional. Request headers.</param>
        /// <returns>Returns DBCS response or throws exception</returns>
        Task DBWorkspaceNotificationWithAccountApi(HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            string audience,
            KeyValuePair<string, string>[] additionalHeaders = null);

        /// <summary>
        /// To call Azure Resource Manager for DB workspace Notifications
        /// </summary>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestUri">The request URI.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="workspaceId">The workspace Id.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="additionalHeaders">Optional. Request headers.</param>
        /// <returns>Returns DBCS response or throws exception</returns>
        Task DBWorkspaceNotificationWithARM(HttpMethod httpMethod,
            Uri requestUri,
            string resourceProviderNamespace,
            string tenantId,
            string workspaceId,
            JToken requestBody,
            KeyValuePair<string, string>[] additionalHeaders = null);
        /// <summary>
        /// Call Control Plane Per Workspace URL
        /// </summary>
        /// <typeparam name="TMessage"></typeparam>
        /// <param name="tenantId">The authentication tenant Id.</param>
        /// <param name="baseUri">The request URI.</param>
        /// <param name="requestPath">absolute path.</param>
        /// <param name="httpMethod">The method.</param>
        /// <param name="requestBody">The request body</param>
        /// <param name="resource">Databricks AAD App ID</param>
        /// <param name="requestHeaders">The request headers.</param>
        /// <param name="queryParameters">The query parameters.</param>
        /// <returns></returns>
        Task<TMessage> CallEndpoint<TMessage>(
        string tenantId,
        string baseUri,
        string requestPath,
        HttpMethod httpMethod,
        TMessage requestBody,
        string resource,
        InsensitiveDictionary<string> requestHeaders = null,
        NameValueCollection queryParameters = null);

        /// <summary>
        /// Gets the graph directory object (if exists) for a given principal id.
        /// </summary>
        /// <param name="tenantId">The id for the tenant that the object belongs under.</param>
        /// <param name="principalId">The id for the graph resource.</param>
        Task<GraphPrincipalDetail> GetGraphDirectoryObjectFromPrincipalId(string tenantId, string principalId);

        /// <summary>
        /// Gets the graph directory object (if exists) for a given App id.
        /// </summary>
        /// <param name="tenantId">The id for the tenant that the object belongs under.</param>
        /// <param name="applicationId">The App Id for the graph resource.</param>
        Task<GraphPrincipalDetail> GetGraphApplicationId(string tenantId, string applicationId);

        /// <summary>
        /// Gets the credentials for the identities from MIRP
        /// </summary>
        /// <param name="identityUrl">The URL of the identity.</param>
        /// <param name="parameters">The Credential API parameters.</param>
        Task<IdentitiesCredential> GetIdentitiesCredentials(
            string identityUrl,
            CredentialRequest parameters);

        /// <summary>
        /// Fetches the latest ARM metadata by querying the ARM endpoint.
        /// </summary>
        /// <returns>The ARM metadata object containing the certificates used by ARM</returns>
        Task<ArmMetadata> FetchLatestMetadata();

        /// <summary>
        /// Get db Private Link Resources
        /// </summary>
        /// <param name="workspaceId">Arm Resource ID</param>
        /// <param name="apiVersion">DB API Version</param>
        /// <param name="resourceProviderNamespace">The resource provider namespace.</param>
        /// <returns>Returns dbPrivateLinkResources</returns>
        Task<List<PrivateLinkResource>> GetDbPrivateLinkResources(string workspaceId, string apiVersion,
            string resourceProviderNamespace);

        /// <summary>
        /// Checks if Disks are present in Resource Group
        /// </summary>
        /// <param name="authenticationTenantId"></param>
        /// <param name="resourceProviderNamespace"></param>
        /// <param name="subscriptionId"></param>
        /// <param name="resourceGroupName"></param>
        /// <param name="apiVersion"></param>
        /// <returns>The DisksExistInResourceGroupResponse</returns>
        Task<DisksExistInResourceGroupResponse> CheckIfDisksExistInResourceGroup(string authenticationTenantId, string resourceProviderNamespace, string subscriptionId, string resourceGroupName, string apiVersion);

        /// <summary>
        /// Checks if virtual machines exist in resource group
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <param name="subscriptionId">The subscription id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="apiVersion">The api version.</param>
        /// <returns>Returns if virtual machines exist in the resource group.</returns>
        Task<bool> CheckIfVirtualMachineExistInResourceGroup(string authenticationTenantId, string resourceProviderNamespace, string subscriptionId, string resourceGroupName, string apiVersion);

        /// <summary>
        /// Deletes a Private endpoint connection.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="privateEndPointConnectionId">The private endpoint connection Id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<Uri> DeletePrivateEndpointConnection(
            string authenticationTenantId,
            string privateEndPointConnectionId,
            string resourceProviderNamespace);

        /// <summary>
        /// Delete an Access Connector Resource
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="accessConnectorId">The access connector resource id.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<Uri> DeleteAccessConnector(
            string authenticationTenantId,
            string accessConnectorId,
            string resourceProviderNamespace);

        /// <summary>
        /// Deletes a list of resources specified in the request body
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token</param>
        /// <param name="requestBody">Request body contains the resources to be deleted</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        /// <returns></returns>
        Task<Uri> DeleteResourcesInBulk(string authenticationTenantId, JObject requestBody, string resourceProviderNamespace);

        /// <summary>
        /// Gets a security rule.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="securityGroupName">The security group name.</param>
        /// <param name="securityRuleName">The security rule name.</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task<SecurityRuleDefinition> GetNsgSecurityRule(
            string authenticationTenantId,
            string resourceGroupName,
            string securityGroupName,
            string securityRuleName,
            string resourceProviderNamespace);

        /// <summary>
        /// Create or update a security rule.
        /// </summary>
        /// <param name="authenticationTenantId">The tenant Id to be used to get authentication token.</param>
        /// <param name="subscriptionId">The subscription Id.</param>
        /// <param name="resourceGroupName">The resource group name.</param>
        /// <param name="securityGroupName">The security group name.</param>
        /// <param name="securityRuleName">The security rule name.</param>
        /// <param name="securityRuleDefinition">The security rule definition</param>
        /// <param name="resourceProviderNamespace">The resource provider name space.</param>
        Task CreateOrUpdateNsgSecurityRule(
            string authenticationTenantId,
            string resourceGroupName,
            string securityGroupName,
            string securityRuleName,
            SecurityRuleDefinition securityRuleDefinition,
            string resourceProviderNamespace);
    }
}
