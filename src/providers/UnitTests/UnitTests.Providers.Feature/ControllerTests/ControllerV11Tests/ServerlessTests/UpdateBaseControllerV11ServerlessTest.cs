﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV11Tests
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers.ControllerV11;
    using Moq;
    using Newtonsoft.Json.Linq;

    public class UpdateBaseControllerV11ServerlessTest : BaseControllerV11ServerlessTest
    {
        #region Mock the controller
        private DatabricksV11Controller databricksV11Controller;

        /// <summary>
        /// Gets the controller object whose public methods are being tested in the unit test.
        /// </summary>
        protected DatabricksV11Controller DatabricksController
        {
            get
            {
                if (databricksV11Controller == null)
                {
                    databricksV11Controller = new DatabricksV11Controller(
                        providerConfiguration: this.ApplicationProviderConfiguration.Object,
                        accessConnectorProviderConfiguration: this.AccessConnectorProviderConfiguration.Object);
                    BaseControllerTest.SetupController(databricksV11Controller);
                }

                return databricksV11Controller;
            }
        }

        private Mock<IFrontdoorEngine> frontdoorEngineMock;
        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngineMock == null)
                {
                    frontdoorEngineMock = base.FrontdoorEngineMock;

                    var subscriptionRegistered = new FeatureDefinition
                    {
                        Properties = JToken.Parse("{\"state\": \"Registered\"}")
                    };

                    this.frontdoorEngineMock.Setup<Task<FeatureDefinition>>(o => o.GetFeatureRegistrationByName(
                            It.Is<string>(tenantId => tenantId == Constants.Common.HomeTenantId),
                            It.Is<string>(subId => subId == Constants.Common.SubscriptionId),
                            It.Is<string>(rpNamespace => rpNamespace == Constants.Common.ResourceProviderNamespace),
                            It.Is<string>(featureName =>
                                featureName == ProviderConstants.Databricks.ServerlessWorkspaceFeatureFlagKey)
                        ))
                        .ReturnsAsync(subscriptionRegistered);

                    this.frontdoorEngineMock.Setup(
                    o => o.GetAmlWorkspace(
                        It.Is<string>(authTenantId => authTenantId == Constants.Common.HomeTenantId),
                        It.Is<string>(amlWorkspaceId => amlWorkspaceId == Constants.AMLLinking.AMLWorkspaceId),
                        It.IsAny<string>()
                        )).ReturnsAsync(new JObject());

                    // Add mock setup for DBWorkspaceUpdateNotificationWrapper
                    this.frontdoorEngineMock.Setup(o => o.DBWorkspaceUpdateNotificationWrapper(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<JToken>(),
                        It.IsAny<string>(),
                        It.IsAny<bool>(),
                        It.IsAny<Uri>(),
                        It.IsAny<KeyValuePair<string, string>[]>()))
                    .ReturnsAsync(new WorkspaceUpdateResponse
                    {
                        Status = OperationStatus.Success,
                        RequiresPolling = false
                    });
                }

                return this.frontdoorEngineMock;
            }
        }
        #endregion

        #region Arrange the existing entity saved in the database
        protected virtual JToken ExistingEntitySku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private ApplianceEntity existingApplianceEntity;
        protected override ApplianceEntity ExistingApplianceEntity
        {
            get
            {
                if (existingApplianceEntity == null)
                {
                    existingApplianceEntity = new ApplianceEntity
                    {
                        SubscriptionId = Constants.Common.SubscriptionId,
                        ResourceGroup = Constants.Common.ResourceGroupName,
                        Name = Constants.Common.ApplicationName,
                        Sku = this.ExistingEntitySku,
                        Location = Constants.Common.Location,
                        Properties = this.ExistingWorkspaceProperties,
                        Metadata = this.ExistingApplianceMetadata
                    };
                }

                return existingApplianceEntity;
            }
        }


        private ApplicationProperties existingWorkspaceProperties;
        protected virtual ApplicationProperties ExistingWorkspaceProperties
        {
            get
            {
                if (existingWorkspaceProperties == null)
                {
                    existingWorkspaceProperties = new ApplicationProperties
                    {
                        ComputeMode = Constants.Common.ServerlessComputeMode,
                        ProvisioningState = ProvisioningState.Succeeded,
                        WorkspaceId = Constants.Common.InternalWorkspaceId,
                        WorkspaceUrl = Constants.Common.WorkspaceUrl,
                        Parameters = this.ExistingWorkspaceParameters,
                        PublisherPackageId = Constants.Common.PublisherPackageId,
                        CreatedBy = new ApplicationClientDetailsEntity
                        {
                            ApplicationId = Constants.Common.CreatedByAppId,
                            Oid = Constants.Common.CreatedByPrincipalOid,
                            Puid = Constants.Common.CreatedByPrincipalLegacyPuid
                        }
                    };
                }

                return existingWorkspaceProperties;
            }
        }

        private List<ApplicationAuthorization> existingAuthorizationsList;
        protected virtual ApplicationAuthorization[] ExistingAuthorizations
        {
            get
            {
                if (existingAuthorizationsList == null)
                {
                    var authorization = new ApplicationAuthorization
                    {
                        PrincipalId = Constants.Common.AuthorizationPrincipalId,
                        RoleDefinitionId = Constants.Common.AuthorizationRoleDefinitionId
                    };

                    existingAuthorizationsList = new List<ApplicationAuthorization>
                    {
                        authorization
                    };
                }

                return existingAuthorizationsList.ToArray();
            }
        }

        private InsensitiveDictionary<JToken> existingWorkspaceParameters;
        protected virtual InsensitiveDictionary<JToken> ExistingWorkspaceParameters
        {
            get
            {
                if (existingWorkspaceParameters == null)
                {
                    existingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                }
                return existingWorkspaceParameters;
            }
        }

        private ApplianceMetadata existingApplianceMetadata;
        protected virtual ApplianceMetadata ExistingApplianceMetadata
        {
            get
            {
                if (existingApplianceMetadata == null)
                {
                    existingApplianceMetadata = new ApplianceMetadata()
                    {
                        OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus> { }
                    };
                }

                return existingApplianceMetadata;
            }
        }

        #endregion
    }
}
