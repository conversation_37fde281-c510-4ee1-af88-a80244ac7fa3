﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Feature.ControllerTests.ControllerV1Tests.PutApplicationTests.UpdateTests.BasicWorkspaceTests
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using System.Web.Http;
    using System.Web.Http.Hosting;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Feature.Controllers;
    using Moq;
    using Newtonsoft.Json.Linq;

    /// <summary>
    /// This test captures expected behaviour when DB notification fails during a PUT request
    /// We are trying to upgrade a workspace from standard to premium, but we have mocked the DB notification method
    /// to throw an exception.
    /// The test cases here document the behaviour when that happens.
    /// </summary>
    [TestClass]
    public class DBNotificationFailureTest: UpdateBaseControllerV1Test
    {
        protected override JToken ExistingEntitySku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Standard""}");
            }
        }

        protected override JToken Sku
        {
            get
            {
                return JToken.Parse(@"{ ""name"": ""Premium""}");
            }
        }

        private Mock<IFrontdoorEngine> frontdoorEngineMock;

        protected override Mock<IFrontdoorEngine> FrontdoorEngineMock
        {
            get
            {
                if (frontdoorEngineMock == null)
                {
                    frontdoorEngineMock = base.FrontdoorEngineMock;


                    frontdoorEngineMock.Setup(o => o.DBWorkspaceNotificationWrapper(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<JToken>(),
                        It.IsAny<string>(),
                        It.IsAny<bool>(),
                        It.IsAny<KeyValuePair<string, string>[]>()
                        )
                    ).Throws(new Exception());
                }

                return frontdoorEngineMock;
            }
        }

        [TestMethod("When call to db notification fails it should return a conflict status code")]
        public async Task TestReturnsConflictStatusCode()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var ex = await Assert.ThrowsExceptionAsync<ErrorResponseMessageException>(
                async () => await this.DatabricksController.PutApplication(
                     Constants.Common.SubscriptionId,
                     Constants.Common.ResourceGroupName,
                     Constants.Common.ApplicationName,
                     this.GetRequestObject<WorkspaceV1>()));

                Assert.AreEqual(ErrorResponseCode.WorkspacePatchFailed, ex.ErrorCode);
                Assert.AreEqual(HttpStatusCode.Conflict, ex.HttpStatus);
            }
        }
    }
}
