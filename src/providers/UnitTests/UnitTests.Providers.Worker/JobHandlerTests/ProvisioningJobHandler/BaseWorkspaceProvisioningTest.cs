﻿//-----------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//-----------------------------------------------------------

namespace UnitTests.Providers.Worker
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Extensions;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.DataProviders;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.DBAsyncContract;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Metadata;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.AccessConnector;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions.ManagedIdentity;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Entities;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Logging;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Worker.JobHandlers;
    using Moq;
    using Newtonsoft.Json.Linq;

    public class BaseWorkspaceProvisioningTest
    {
        protected ApplianceProvisioningJobMetadata Metadata;
        protected MarketplaceAppliancePackage MarketPlaceAppliancePackage;
        protected List<ApplicationAuthorization> Authorizations;
        protected ApplianceEntity ApplianceEntity;
        protected AccessConnectorEntity AccessConnectorEntity;
        protected ApplianceEntity SavedApplianceEntity;
        protected ApplianceEntity ReplacedApplianceEntity;
        protected ApplianceProvisioningJobHandler ProvisioningHandler;
        protected Mock<ILogger> Logger;
        protected Mock<IFrontdoorEngine> FrontdoorEngine;
        protected Mock<IApplianceEngine> ApplianceEngine;
        protected Mock<IApplianceDataProvider> ApplianceDataProvider;
        protected Mock<IAccessConnectorDataProvider> AccessConncetorDataProvider;
        protected Mock<IApplianceDataProvider> ApplicationDataProvider;
        protected Mock<IApplicationPackageDataProvider> MarketPlacePackageDataProvider;
        private Dictionary<string, string> configurationSettings;

        /// <summary>
        /// Override this to set feature flags
        /// </summary>
        protected virtual Dictionary<string, string> ConfigurationSettings
        {
            get
            {
                if (configurationSettings == null)
                {
                    configurationSettings = new Dictionary<string, string>();
                    configurationSettings.Add("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.UnregisterManagedByTenantEnabled", Constants.UnregisterManagedByTenantEnabled.ToString());
                    configurationSettings.Add("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.RegisterManagedByTenantApiVersion", Constants.RegisterManagedByTenantApiVersion);
                    configurationSettings.Add("Microsoft.WindowsAzure.ResourceStack.Providers.Databricks.Workspace.PublisherPackageIdMappings", "2016 - 09 - 01 - preview#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2017-08-01-preview#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2017-09-01-preview#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2;2018-03-01#databricks.databricks-workspace-dev-previewdatabricks-workspace-dev.0.0.12;2018-03-15#databricks.databricks-workspace-staging-previewdatabricks-workspace-staging.0.0.12;2018-04-01#databricks.databricks-workspace-previewdatabricks-workspace.0.0.2");
                    configurationSettings.Add("Microsoft.WindowsAzure.ResourceStack.Providers.ApplianceProvisioningJob.MaxRetries", "1");
                    configurationSettings.Add("Microsoft.WindowsAzure.ResourceStack.Providers.Appliances.DatabricksApplicationId", "abcd-edgh");

                    configurationSettings.Add("EnableDBWorkspaceNotification", "true");
                }

                return configurationSettings;
            }
        }

        [TestInitialize]
        public virtual void SetUp()
        {
            this.SetUpCorrelationContext();
            this.SetupMetadata();
            this.SetupApplianceEntity();
            this.SetupAcessConnectorEntity();
            this.SetUpAuthorizations();
            this.SetUpMarketPlaceAppliancePackage();

            this.MockLogger();
            this.MockFrontdoorEngine();

            this.MockApplianceDataProvider();
            this.MockAccessConnectorDataProvider();
            this.MockApplicationDataProvider();
            this.MockMarketPlacePackageDataProvider();
            this.MockApplianceEngine();

            ProvisioningHandler = new ApplianceProvisioningJobHandler(
                logger: this.Logger.Object,
                frontdoorEngine: this.FrontdoorEngine.Object,
                applianceEngine: this.ApplianceEngine.Object,
                applianceDataProvider: this.ApplianceDataProvider.Object,
                accessConnectorDataProvider: this.AccessConncetorDataProvider.Object,
                marketPlacePackageDataProvider: this.MarketPlacePackageDataProvider.Object,
                metadata: this.Metadata,
                unregisterManagedByTenantEnabled: Constants.UnregisterManagedByTenantEnabled,
                registerManagedByTenantApiVersion: Constants.RegisterManagedByTenantApiVersion,
                totalExecutedCount: Constants.TotalExecutedCount);
        }

        [TestCleanup]
        public virtual void TearDown()
        {
            RequestCorrelationContext.Current.Dispose();
        }

        protected virtual void SetUpCorrelationContext()
        {
            RequestCorrelationContext context = new RequestCorrelationContext();

            var additionalProperties = new OrdinalDictionary<string>();
            additionalProperties.Add("x-ms-home-tenant-id", Constants.HomeTenantId);
            context.SetAdditionalProperties(additionalProperties);

            var identity = new RequestIdentity();
            identity.Claims = new Dictionary<string, string>();
            identity.Claims.Add(Constants.PrincipalOidClaim, Constants.CreatedByPrincipalOid);
            identity.Claims.Add(Constants.PrincipalLegacyPuidClaim, Constants.CreatedByPrincipalLegacyPuid);
            identity.Claims.Add(Constants.PrincipalApplicationIdClaim, Constants.CreatedByAppId);
            context.Initialize(apiVersion: Constants.ApiVersion);
            context.SetAuthenticationIdentity(identity);

            RequestCorrelationContext.Current.Initialize(context);
            RequestCorrelationContext.Current.CorrelationId = Constants.CorrelationId;
        }

        protected virtual void SetupMetadata()
        {
            this.Metadata = new ApplianceProvisioningJobMetadata()
            {
                RequestCorrelationContext = RequestCorrelationContext.Current,
                SubscriptionId = Constants.SubscriptionId,
                ResourceGroupName = Constants.ResourceGroupName,
                ResourceType = Constants.ResourceType,
                ResourceProviderNamespace = Constants.ResourceProviderNamespace,
                ApplianceName = Constants.ApplicationName,
                ProvidersLocation = Constants.Location,
                Parameters = new InsensitiveDictionary<JToken>(),
                ApplianceProvisioningState = ApplianceProvisioningState.Initializing,
                ApplianceWriteOperationName = Constants.ApplicationWriteOperationName,
                CurrentRetryableErrorCount = 0,
                CurrentDeploymentOperationExceptionRetryableErrorCount = 0,
                ResourceOperation = ProvisioningOperation.Create
            };
        }

        protected virtual void SetUpAuthorizations()
        {
            Authorizations = new List<ApplicationAuthorization>();
            var authorization = new ApplicationAuthorization();
            authorization.PrincipalId = Constants.AuthorizationPrincipalId;
            authorization.RoleDefinitionId = Constants.AuthorizationRoleDefinitionId;
            Authorizations.Add(authorization);
        }

        protected virtual void SetUpMarketPlaceAppliancePackage()
        {
            this.MarketPlaceAppliancePackage = new MarketplaceAppliancePackage();
            this.MarketPlaceAppliancePackage.IsEnabled = true;
            this.MarketPlaceAppliancePackage.Authorizations = Authorizations.ToArray();
            this.MarketPlaceAppliancePackage.TenantId = Constants.PublisherTenantId;
        }

        protected virtual void SetupApplianceEntity()
        {
            this.ApplianceEntity = GetApplianceEntity();
        }

        protected AccessConnectorEntity GetAccessConnectorEntity()
        {
            return new AccessConnectorEntity()
            {
                SubscriptionId = Constants.SubscriptionId,
                ResourceGroup = Constants.ResourceGroupName,
                Name = Constants.ConnectorName,
                Location = Constants.Location,
                Tags = new InsensitiveDictionary<string>(),
                Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned,
                    PrincipalId = Constants.ExcludedPrincipalId,
                    TenantId = Constants.HomeTenantId
                },
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                    ReferedBy = new List<string>()
                    {
                        $"/subscriptions/{Constants.SubscriptionId}/resourceGroups/{Constants.ResourceGroupName}/providers/{Constants.ResourceProviderNamespace}/{Constants.ApplicationName}"
                    }
                }
            };
        }

        protected ApplianceEntity GetApplianceEntity()
        {
            var managedResourceGroupRoleAssignments = new InsensitiveDictionary<string>();
            managedResourceGroupRoleAssignments.Add($"{Constants.AuthorizationPrincipalId}/{Constants.AuthorizationRoleDefinitionId}", Guid.NewGuid().ToString());

            var readerRoleAssignmentIds = new InsensitiveDictionary<string>();
            readerRoleAssignmentIds.Add($"{Constants.AuthorizationPrincipalId}/{Constants.AuthorizationRoleDefinitionId}", Guid.NewGuid().ToString());

            var applicationResourceRoleAssignments = new InsensitiveDictionary<string>();
            applicationResourceRoleAssignments.Add($"{Constants.AuthorizationPrincipalId}/{Constants.AuthorizationRoleDefinitionId}", Guid.NewGuid().ToString());

            var applicationResourceReaderRoleAssignments = new InsensitiveDictionary<string>();
            applicationResourceReaderRoleAssignments.Add($"{Constants.AuthorizationPrincipalId}/{Constants.AuthorizationRoleDefinitionId}", Guid.NewGuid().ToString());

            var applicationProperties = new ApplicationProperties
            {
                ManagedResourceGroupId = $"/subscriptions/{Constants.SubscriptionId}/resourceGroups/{Constants.ManagedResourceGroupName}",
                ProvisioningState = ProvisioningState.Accepted,
                PublisherPackageId = Constants.PublisherPackageId,
                CreatedBy = new ApplicationClientDetailsEntity
                {
                    Oid = Constants.CreatedByPrincipalOid,
                    Puid = Constants.CreatedByPrincipalLegacyPuid,
                    ApplicationId = Constants.CreatedByAppId
                },
                UpdatedBy = new ApplicationClientDetailsEntity
                {
                    Oid = Constants.CreatedByPrincipalOid,
                    Puid = Constants.CreatedByPrincipalLegacyPuid,
                    ApplicationId = Constants.CreatedByAppId
                },
                Parameters = new InsensitiveDictionary<JToken>()
                {
                    {"customVirtualNetworkId", new JObject() { { "value", "/resource/subid/resourcegroup/Microsoft.Network/vnet/vnetname" } } },
                },
                EncryptionProperties = new EncryptionPropertiesEntity()
                {
                    EncryptionEntities = new EncryptionTargetsEntity()
                    {
                        ManagedDisk = new ManagedDiskEncryptionEntity()
                        {
                            KeyVaultProperties = new ManagedEncryptionKeyVaultEntity()
                            {
                                KeyName = "keyname",
                                KeyVaultUri = "https://keyvaultname.vault.azure.net/"
                            }
                        }
                    }
                },
                AccessConnector = new AccessConnectorIdEntity()
                {
                    Id = $"/subscriptions/{Constants.SubscriptionId}/resourceGroups/{Constants.ResourceGroupName}/providers/Microsoft.Databricks/accessConnectors/{Constants.ConnectorName}"
                },
                WorkspaceUrl = Constants.WorkspaceUrl
            };

            var metadata = new ApplianceMetadata
            {
                ManagedResourceGroupLockName = Constants.ApplicationName,
                DeploymentName = Constants.ApplicationName,

                OperationStatuses = new InsensitiveDictionary<ApplicationOperationStatus>
                    {
                        {
                            Constants.ApplicationWriteOperationName,
                            new ApplicationOperationStatus { Status = ProvisioningState.Accepted }
                        }
                    }
            };

            return new ApplianceEntity()
            {
                SubscriptionId = Constants.SubscriptionId,
                ResourceGroup = Constants.ResourceGroupName,
                Name = Constants.ApplicationName,
                Tags = new InsensitiveDictionary<string>(),
                Location = Constants.Location,
                Sku = new JObject()
                {
                    {"name", Constants.SKU }
                },
                Properties = applicationProperties,
                Metadata = metadata
            };
        }
        protected virtual void SetupAcessConnectorEntity()
        {
            this.AccessConnectorEntity = new AccessConnectorEntity()
            {
                SubscriptionId = Constants.SubscriptionId,
                ResourceGroup = Constants.ResourceGroupName,
                Name = Constants.ConnectorName,
                Location = Constants.Location,
                Tags = new InsensitiveDictionary<string>(),
                Identity = new Identity()
                {
                    Type = IdentityType.SystemAssigned,
                    PrincipalId = Constants.ExcludedPrincipalId,
                    TenantId = Constants.HomeTenantId
                },
                Properties = new AccessConnectorProperties()
                {
                    ProvisioningState = AccessConnectorState.Succeeded,
                }
            };
        }

        protected virtual void MockLogger()
        {
            Logger = new Mock<ILogger>();
        }

        /// <summary>
        /// Sets up the mock for GetDatabricksAccountApiEnpointAndAudience with the specified useArm value
        /// </summary>
        /// <param name="useArm">Whether to use ARM for the operation (true) or Account API (false)</param>
        protected void SetupDatabricksAccountApiEndpointMock(bool useArm)
        {
            this.FrontdoorEngine.Setup(
                o => o.GetDatabricksAccountApiEnpointAndAudience(
                    It.IsAny<string>(),
                    It.IsAny<string>())).ReturnsAsync
                    ((new Uri("http://anyurl.com"), "", useArm));
        }

        protected virtual void MockFrontdoorEngine()
        {
            FrontdoorEngine = new Mock<IFrontdoorEngine>();
            FrontdoorEngine.Setup(o => o.GetDatabricksAccountApiEnpointAndAudience(It.IsAny<string>(), It.IsAny<string>()))
                        .Returns(Task.FromResult((new Uri(Constants.FrontDoorEngineUri), "mockAudience",false)));
            // Todo: Mock RegisterSubscriptionWithManagedTenant
            FrontdoorEngine.Setup(
                    o => o.RegisterSubscriptionWithManagedTenant(
                        Constants.HomeTenantId,
                        Constants.SubscriptionId,
                        Constants.PublisherTenantId,
                        Constants.ResourceProviderNamespace,
                        Constants.RegisterManagedByTenantApiVersion,
                        $"/subscriptions/{Constants.SubscriptionId}/resourcegroups/{Constants.ResourceGroupName}/providers/{Constants.ResourceProviderNamespace}/{Constants.ApplicationName}"
                        )
                    ).Verifiable();

            var featureRegistration = new FeatureDefinition();
            featureRegistration.Properties = JToken.Parse("{\"state\": \"NotRegistered\"}");

            FrontdoorEngine.Setup(
                    o => o.GetFeatureRegistrationByName(
                        Constants.HomeTenantId,
                        Constants.SubscriptionId,
                        Constants.ResourceProviderNamespace,
                        Constants.DisableNetworkIntentPoliciesForSubscription)
                    ).ReturnsAsync(featureRegistration);


            FrontdoorEngine.Setup(
                    o => o.GetRegisteredFeaturesInSubscription(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>()))
                    .Returns(Task.FromResult(new List<string>()));

            FrontdoorEngine.Setup(
                    o => o.CallDatabricksAsyncAccountApi(
                        It.IsAny<HttpMethod>(),
                        It.IsAny<Uri>(),
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<CredMgrNotification>(),
                        It.IsAny<InsensitiveDictionary<string>>()))
                .Returns(Task.CompletedTask);

            FrontdoorEngine.Setup(o => o.FrontdoorEndpointUri).Returns(new Uri("http://localhost"));
            FrontdoorEngine.Setup(o => o.GetDbWorkspaceDetailsAsync<DatabricksWorkspace>(It.IsAny<Uri>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>()))
                        .Returns(Task.FromResult(new DatabricksWorkspace()
                        {
                            Properties = new AsyncWorkspaceProperties()
                            {
                                OperationStatus = new DBOperationStatusObject()
                                {
                                    OperationStatus = DBOperationStatus.Succeeded,
                                },
                            }
                        }));

            // Add default mock setup for the new DBWorkspaceUpdateNotificationWrapper method
            FrontdoorEngine.Setup(o => o.DBWorkspaceUpdateNotificationWrapper(
                It.IsAny<HttpMethod>(),
                It.IsAny<Uri>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<JToken>(),
                It.IsAny<string>(),
                It.IsAny<bool>(),
                It.IsAny<Uri>(),
                It.IsAny<KeyValuePair<string, string>[]>()))
            .ReturnsAsync(new WorkspaceUpdateResponse
            {
                Status = OperationStatus.Success,
                RequiresPolling = false // Default to no polling for ARM scenarios
            });

        }

        protected virtual void MockApplianceEngine()
        {
            ApplianceEngine = new Mock<IApplianceEngine>();

            var operationResponse = new OperationResponse();
            operationResponse.Status = OperationStatus.Success;

            ApplianceEngine.Setup(
                    o => o.CreateManagedResourceGroupIfNotExists(
                        this.ApplianceEntity,
                        Constants.PublisherTenantId,
                        Constants.HomeTenantId,
                        Constants.ResourceType,
                        Constants.ResourceProviderNamespace)
                    ).Verifiable();

            ApplianceEngine.Setup(
                    o => o.CreateAuthorizationForApplication(
                        this.ApplianceEntity,
                        this.MarketPlaceAppliancePackage,
                        Constants.HomeTenantId,
                        Constants.PublisherTenantId,
                        Constants.ResourceType,
                        Constants.ResourceProviderNamespace,
                        null)
                    ).Verifiable();

            ApplianceEngine.Setup(
                    o => o.ConfigureDefaultStorageFirewall(
                            It.IsAny<ApplianceEntity>(),
                            It.IsAny<AccessConnectorIdEntity>(),
                            It.IsAny<DefaultStorageFirewall?>(),
                            It.IsAny<string>(),
                            It.IsAny<string>())
                    ).ReturnsAsync(Tuple.Create((JobExecutionResult)null, (AccessConnectorIdEntity)null, (DefaultStorageFirewall?)null));
        }

        protected virtual void MockApplianceDataProvider()
        {
            this.ApplianceDataProvider = new Mock<IApplianceDataProvider>();

            this.ApplianceDataProvider.Setup(
                o => o.FindAppliance(Constants.SubscriptionId, Constants.ResourceGroupName, Constants.ApplicationName)
                ).ReturnsAsync(this.ApplianceEntity);

            this.ApplianceDataProvider.Setup(
                    o => o.SaveAppliance(It.IsAny<ApplianceEntity>())).Callback<ApplianceEntity>(a => this.SavedApplianceEntity = a);

            this.ApplianceDataProvider.Setup(
                    o => o.ReplaceAppliance(It.IsAny<ApplianceEntity>())).Callback<ApplianceEntity>(a => this.ReplacedApplianceEntity = a);
        }

        protected virtual void MockAccessConnectorDataProvider()
        {
            this.AccessConncetorDataProvider = new Mock<IAccessConnectorDataProvider>();
        }

        protected virtual void MockApplicationDataProvider()
        {
            ApplicationDataProvider = new Mock<IApplianceDataProvider>();
        }

        private void MockMarketPlacePackageDataProvider()
        {
            MarketPlacePackageDataProvider = new Mock<IApplicationPackageDataProvider>();
            MarketPlacePackageDataProvider.Setup(
               o => o.FindMarketplaceAppliancePackage(Constants.PublisherPackageId)
               ).Returns(MarketPlaceAppliancePackage);
        }
    }
}
