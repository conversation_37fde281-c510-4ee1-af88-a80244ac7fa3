﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
using Microsoft.WindowsAzure.ResourceStack.Common.Json;
using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Entities;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
using Moq;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Net.Http;

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.DBWorkspaceNotification
{
    [TestClass]
    public class DBWorkspaceNotificationSucceedsTest : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeDBWorkspaceNotificationSucceedsTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };
        }

        [TestMethod("Job execution is set to succeeded")]
        public void TestApplianceProvisioningJobStatus()
        {
            this.SetupDatabricksAccountApiEndpointMock(useArm: true);
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, postponeExecutionResult.Status);
            }
        }

        [TestMethod("Job execution is set to postponed with AccountApiPolling")]
        public void TestApplianceProvisioningJobStatusWithAccountApi()
        {
            this.SetupDatabricksAccountApiEndpointMock(useArm: false);

            // Setup the new method to return polling required for Account API
            this.FrontdoorEngine.Setup(o => o.DBWorkspaceUpdateNotificationWrapper(
                It.IsAny<HttpMethod>(),
                It.IsAny<Uri>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<JToken>(),
                It.IsAny<string>(),
                false, // useArm = false for Account API
                It.IsAny<Uri>(),
                It.IsAny<KeyValuePair<string, string>[]>()))
            .ReturnsAsync(new WorkspaceUpdateResponse
            {
                Status = OperationStatus.Success,
                RequiresPolling = true,
                PollingState = AccountApiPollingState.WaitingForUpdate,
                OperationId = "test-workspace-id",
                BaseUri = new Uri("http://test.com"),
                Audience = "test-audience",
                PollingIntervalSeconds = 30,
                ResourceOperation = ProvisioningOperation.AccountApiPolling
            });

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, postponeExecutionResult.Status);
            }
        }
        [TestMethod("ReplaceAppliance is called to replace existing appliance with updated appliance")]
        public void VerifyReplaceApplianceIsCalled()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;

                this.ApplianceDataProvider.Verify(
                        o => o.ReplaceAppliance(
                            It.Is<ApplianceEntity>(
                                entity =>
                                    entity == this.ApplianceEntity
                                    )));
            }
        }
    }
}
