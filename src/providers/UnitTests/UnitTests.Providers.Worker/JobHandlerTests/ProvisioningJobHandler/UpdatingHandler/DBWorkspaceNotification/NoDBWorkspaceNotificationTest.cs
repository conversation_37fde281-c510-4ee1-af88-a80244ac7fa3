﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.DBWorkspaceNotification
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class NoDBWorkspaceNotificationTest : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeNoDBWorkspaceNotificationTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            this.ApplianceEntity.Properties.Parameters.Add("amlWorkspaceId", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""test""}")); ;

            //Setup GetAmlWorkspace to return null
            FrontdoorEngine.Setup(o => o.GetAmlWorkspace(
                       this.MarketPlaceAppliancePackage.TenantId,
                       It.IsAny<string>(),
                       ProviderConstants.Databricks.ResourceProviderNamespace
                       )
                   ).ReturnsAsync(() => null);
        }

        [TestMethod("Job execution is set to Faulted")]
        public void TestApplianceProvisioningJobStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;

                Assert.AreEqual(JobExecutionStatus.Faulted, result.Status);
            }
        }

        [TestMethod("Verfiy No DB calls are made when Linked AML does not exists")]
        public void TestApplianceProvisioningStatus()
        {
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var _ = this.ProvisioningHandler.OnJobExecute().Result;

                this.FrontdoorEngine.Verify(
                    o => o.DBWorkspaceUpdateNotificationWrapper(
                            It.IsAny<HttpMethod>(),
                            It.IsAny<Uri>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<string>(),
                            It.IsAny<JToken>(),
                            It.IsAny<string>(),
                            It.IsAny<bool>(),
                            It.IsAny<Uri>(),
                            It.IsAny<KeyValuePair<string, string>[]>()), Times.Never);
            }
        }
    }
}
