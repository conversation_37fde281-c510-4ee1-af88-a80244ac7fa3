﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.StorageSKUUpdate
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net;
    using System.Text;
    using System.Threading.Tasks;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Instrumentation;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Components;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Engines;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.ErrorResponses;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class ConfigureStorageSkuTest : BaseWorkspaceUpdateTest
    {
        private InsensitiveDictionary<JToken> incomingWorkspaceParameters;
        protected override InsensitiveDictionary<JToken> IncomingWorkspaceParameters
        {
            get
            {
                if (incomingWorkspaceParameters == null)
                {
                    incomingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                    incomingWorkspaceParameters.Add("enableNoPublicIp", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                    incomingWorkspaceParameters.Add(ProviderConstants.Databricks.StorageAccountNameParameter, JToken.Parse(@"{ ""type"": ""String"", ""value"": """ + Constants.StorageAccountName + @"""}"));
                    incomingWorkspaceParameters.Add("storageAccountSkuName", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""Standard_GRS""}"));
                }

                return incomingWorkspaceParameters;
            }
        }

        protected InsensitiveDictionary<JToken> IncomingWorkspaceLRSStorageSkuParameters
        {
            get
            {
                incomingWorkspaceParameters = new InsensitiveDictionary<JToken>();
                incomingWorkspaceParameters.Add("enableNoPublicIp", JToken.Parse(@"{ ""type"": ""Bool"", ""value"": false}"));
                incomingWorkspaceParameters.Add("storageAccountSkuName", JToken.Parse(@"{ ""type"": ""String"", ""value"": ""Standard_LRS""}"));
                return incomingWorkspaceParameters;
            }
        }

        [TestInitialize]
        public void UpdateStorageSkuTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;
            this.IncomingWorkspaceProperties.Parameters = this.IncomingWorkspaceParameters;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            this.ApplianceEntity.Properties.Parameters = incomingWorkspaceParameters;

            var storageOperationexception = new StorageOperationException(
                                HttpStatusCode.InternalServerError,
                                ErrorResponseCode.StorageAccountOperationFailed.ToString(),
                                ErrorResponseMessages.StorageAccountUpdateFailed
                );

            var storageUpdateRequest = new StorageDefinition()
            {
                Sku = new JObject()
                    {
                        new JProperty(ProviderConstants.Databricks.NameProperty, Constants.StorageAccountStandardSku)
                    }
            };

            FrontdoorEngine.Setup(o => o.StorageApiVersion).Returns(Constants.StorageApiVersion);

            FrontdoorEngine.Setup(
                        o => o.PatchStorageResource(
                            GetStorageUri(),
                            ProviderConstants.Databricks.ResourceProviderNamespace,
                            Constants.HomeTenantId,
                            storageUpdateRequest
                            )).ReturnsAsync(new System.Net.Http.HttpResponseMessage() { StatusCode = System.Net.HttpStatusCode.OK });
        }

        private static Uri GetStorageUri()
        {
            return UriTemplateEngine.GetStorageGetPropertiesRequestUri(
                                 new Uri(Constants.FrontDoorEngineUri),
                                 Constants.SubscriptionId,
                                 Constants.ManagedResourceGroupName,
                                 Constants.StorageAccountName,
                                 Constants.StorageApiVersion);
        }

        [TestMethod("Job execution is set to succeeded")]
        public void TestApplianceProvisioningJobStatus()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", true));
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, result.Status);
            }
        }

        [TestMethod("Job execution is set to postponed with AccountApi")]
        public void TestApplianceProvisioningJobStatusWithAccountApi()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", false));

            // Setup the new method to return polling required for Account API
            this.FrontdoorEngine.Setup(o => o.DBWorkspaceUpdateNotificationWrapper(
                It.IsAny<HttpMethod>(),
                It.IsAny<Uri>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<JToken>(),
                It.IsAny<string>(),
                false, // useArm = false for Account API
                It.IsAny<Uri>(),
                It.IsAny<KeyValuePair<string, string>[]>()))
            .ReturnsAsync(new WorkspaceUpdateResponse
            {
                Status = OperationStatus.Success,
                RequiresPolling = true,
                PollingState = AccountApiPollingState.WaitingForUpdate,
                OperationId = "test-workspace-id",
                BaseUri = new Uri("http://test.com"),
                Audience = "test-audience",
                PollingIntervalSeconds = 30,
                ResourceOperation = ProvisioningOperation.AccountApiPolling
            });

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var result = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, result.Status);
            }
        }

        [TestMethod("Update workspace storage DBFS SKU")]
        public void TestApplianceProvisioningState()
        {
            this.FrontdoorEngine.Setup(
            o => o.GetDatabricksAccountApiEnpointAndAudience(
                It.IsAny<string>(),
                It.IsAny<string>())).ReturnsAsync
                ((new Uri("http://anyurl.com"), "", true));
            this.IncomingWorkspaceProperties.Parameters = this.IncomingWorkspaceLRSStorageSkuParameters;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            FrontdoorEngine.Setup(
                       o => o.PatchStorageResource(
                           GetStorageUri(),
                           ProviderConstants.Databricks.ResourceProviderNamespace,
                           Constants.PublisherTenantId,
                           It.IsAny<StorageDefinition>()
                           )).ReturnsAsync(new System.Net.Http.HttpResponseMessage() { StatusCode = System.Net.HttpStatusCode.OK });

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, postponeExecutionResult.Status);
            }
        }

        [TestMethod("Validate Appliance provisioning state during job failure")]
        public void TestApplianceProvisioningStateForFailedStorageCall()
        {
            this.IncomingWorkspaceProperties.Parameters = this.IncomingWorkspaceLRSStorageSkuParameters;
            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            var storageException = new StorageOperationException(
                HttpStatusCode.InternalServerError,
                ErrorResponseCode.StorageAccountOperationFailed.ToString(),
                ErrorResponseMessages.StorageAccountUpdateFailed
            );

            FrontdoorEngine.Setup(
                o => o.PatchStorageResource(
                    GetStorageUri(),
                    ProviderConstants.Databricks.ResourceProviderNamespace,
                    Constants.PublisherTenantId,
                    It.IsAny<StorageDefinition>()
                )).ThrowsAsync(storageException);

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Faulted, postponeExecutionResult.Status);
                Assert.AreEqual(this.IncomingApplianceEntity.Properties.ProvisioningState, ProvisioningState.Succeeded);
            }
        }
    }
}
