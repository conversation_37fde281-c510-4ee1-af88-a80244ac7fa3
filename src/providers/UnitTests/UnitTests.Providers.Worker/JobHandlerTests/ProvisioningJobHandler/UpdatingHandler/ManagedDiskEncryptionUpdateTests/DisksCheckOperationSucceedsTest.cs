﻿//------------------------------------------------------------
// Copyright (c) Microsoft Corporation.  All rights reserved.
//------------------------------------------------------------

namespace UnitTests.Providers.Worker.JobHandlerTests.ProvisioningJobHandler.UpdatingHandler.ManagedDiskEncryptionUpdateTests
{
    using System;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using Microsoft.WindowsAzure.ResourceStack.Common.BackgroundJobs;
    using Microsoft.WindowsAzure.ResourceStack.Common.Collections;
    using Microsoft.WindowsAzure.ResourceStack.Common.Json;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Application.Data.Definitions.Enums;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Constants;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Data;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Definitions;
    using Microsoft.WindowsAzure.ResourceStack.Providers.Common.Responses;
    using Moq;
    using Newtonsoft.Json.Linq;

    [TestClass]
    public class DisksCheckOperationSucceedsTest : BaseWorkspaceUpdateTest
    {
        [TestInitialize]
        public void InitializeDisksCheckOperationSucceedsTest()
        {
            this.Metadata.ResourceOperation = ProvisioningOperation.Updating;

            var encryptionProperties = new EncryptionPropertiesEntity()
            {
                EncryptionEntities = new EncryptionTargetsEntity()
                {
                    ManagedDisk = new ManagedDiskEncryptionEntity()
                    {
                        KeySource = ManagedEncryptionKeySource.KeyVault,
                        KeyVaultProperties = new ManagedEncryptionKeyVaultEntity()
                        {
                            KeyName = Constants.ManagedDiskEncryption.KeyName,
                            KeyVaultUri = Constants.ManagedDiskEncryption.KeyVaultUri,
                            KeyVersion = Constants.ManagedDiskEncryption.KeyVersion
                        }
                    }
                }
            };

            this.IncomingWorkspaceProperties.EncryptionProperties = encryptionProperties;

            this.Metadata.Parameters = new InsensitiveDictionary<JToken>
            {
                { ProviderConstants.WorkspaceUpdateParameters.Properties, this.IncomingWorkspaceProperties?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Sku, this.IncomingEntitySku?.ToJToken() },
                { ProviderConstants.WorkspaceUpdateParameters.Tags, this.IncomingApplianceEntity?.Tags?.ToJToken() }
            };

            var disksExistInResourceGroupResponse = new DisksExistInResourceGroupResponse()
            {
                Status = OperationStatus.Success,
                DisksExistsInResourceGroup = false
            };

            this.FrontdoorEngine.Setup(
                        o => o.CheckIfDisksExistInResourceGroup(
                             Constants.PublisherTenantId,
                             ProviderConstants.Databricks.ResourceProviderNamespace,
                             this.Metadata.SubscriptionId,
                             Constants.ManagedResourceGroupName,
                             ProviderConstants.Compute.DiskEncryptionSetApiVersion)).
                             ReturnsAsync(disksExistInResourceGroupResponse);


            this.FrontdoorEngine.Setup(
                      o => o.CreateManagedDiskEncryptionSetWithCmk(
                         this.ApplianceEntity.Properties.ManagedResourceGroupId,
                         Constants.PublisherTenantId,
                         ProviderConstants.Databricks.ResourceProviderNamespace,
                         this.ApplianceEntity.SubscriptionId,
                         It.IsAny<DiskEncryptionSetDefinition>()
                         )).
                         ReturnsAsync(new System.Net.Http.HttpResponseMessage() { StatusCode = System.Net.HttpStatusCode.OK });

            var diskEncryptionSetOperationResponse = new DiskEncryptionSetOperationResponse
            {
                DiskEncryptionSetDefinition = new DiskEncryptionSetDefinition(),
                Status = OperationStatus.Success
            };

            this.ApplianceEngine.Setup(
                o => o.GetDiskEncryptionSetOperationStatus(
                    DiskEncryptionSetOperation.CreateDiskEncryptionSet,
                    It.IsAny<string>(),
                    Constants.PublisherTenantId,
                    ProviderConstants.Databricks.ResourceProviderNamespace,
                    It.IsAny<Uri>()
                    )).ReturnsAsync(diskEncryptionSetOperationResponse);

        }

        [TestMethod("Job execution is set to succeeded")]
        public void TestApplianceProvisioningState()
        {
            this.SetupDatabricksAccountApiEndpointMock(useArm: true);
            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Succeeded, postponeExecutionResult.Status);
            }
        }

        [TestMethod("Job execution is set to postponed with Account Api")]
        public void TestApplianceProvisioningStateWithAccountApi()
        {
            this.SetupDatabricksAccountApiEndpointMock(useArm: false);

            // Setup the new method to return polling required for Account API
            this.FrontdoorEngine.Setup(o => o.DBWorkspaceUpdateNotificationWrapper(
                It.IsAny<HttpMethod>(),
                It.IsAny<Uri>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<JToken>(),
                It.IsAny<string>(),
                false, // useArm = false for Account API
                It.IsAny<Uri>(),
                It.IsAny<KeyValuePair<string, string>[]>()))
            .ReturnsAsync(new WorkspaceUpdateResponse
            {
                Status = OperationStatus.Success,
                RequiresPolling = true,
                PollingState = AccountApiPollingState.WaitingForUpdate,
                OperationId = "test-workspace-id",
                BaseUri = new Uri("http://test.com"),
                Audience = "test-audience",
                PollingIntervalSeconds = 30,
                ResourceOperation = ProvisioningOperation.AccountApiPolling
            });

            using (new AppConfigSettingScope(this.ConfigurationSettings))
            {
                var postponeExecutionResult = this.ProvisioningHandler.OnJobExecute().Result;
                Assert.AreEqual(JobExecutionStatus.Postponed, postponeExecutionResult.Status);
            }
        }
    }
}
